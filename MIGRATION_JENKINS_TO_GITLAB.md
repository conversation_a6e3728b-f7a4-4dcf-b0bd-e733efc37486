# Jenkins to GitLab CI/CD Migration Guide

## Overview

This document outlines the migration from <PERSON> to GitLab CI/CD for the Vietnam Administrative Restructuring 2025 project. The new GitLab CI/CD pipeline provides enhanced security, better integration with GitLab features, and improved maintainability.

## Key Differences

### 1. Configuration Format

**Jenkins (Jenkinsfile)**
```groovy
pipeline {
    agent any
    stages {
        stage('Build') {
            steps {
                script {
                    // Groovy-based scripting
                }
            }
        }
    }
}
```

**GitLab CI (.gitlab-ci.yml)**
```yaml
stages:
  - build
  - test
  - deploy

build:docker:
  stage: build
  image: docker:24.0.5
  script:
    - docker build -t $DOCKER_IMAGE .
```

### 2. Environment Variables

**Jenkins**
- Configured through Jenkins UI
- Environment variables set in pipeline or globally
- Credentials managed through Jenkins Credentials Store

**GitLab CI**
- Configured through GitLab Project Settings > CI/CD > Variables
- Built-in predefined variables (CI_PIPELINE_ID, CI_COMMIT_BRANCH, etc.)
- Masked and protected variables for sensitive data

### 3. Docker Integration

**Jenkins**
- Requires Docker plugin installation
- Manual Docker daemon configuration
- Complex setup for Docker-in-Docker

**GitLab CI**
- Native Docker support with services
- Built-in GitLab Container Registry integration
- Simplified Docker-in-Docker with `docker:dind` service

## Migration Steps

### Step 1: Environment Variables Setup

Configure the following variables in GitLab Project Settings > CI/CD > Variables:

| Variable Name | Type | Environment | Description |
|---------------|------|-------------|-------------|
| `STAGING_GEMINI_API_KEY` | Masked | staging | Gemini API key for staging |
| `PRODUCTION_GEMINI_API_KEY` | Masked, Protected | production | Gemini API key for production |

### Step 2: GitLab Runner Configuration

Ensure your GitLab Runner is configured with:
- Docker executor
- Privileged mode enabled (for Docker-in-Docker)
- Sufficient resources for building Docker images

### Step 3: Container Registry Setup

The pipeline uses GitLab Container Registry:
- Images are automatically tagged with pipeline ID
- Latest tag is pushed for main branch
- Registry authentication is handled automatically

### Step 4: Branch Protection

Configure branch protection rules:
- Main branch requires merge request approval
- Production deployments are manual only
- Staging deployments are automatic for develop branch

## Pipeline Stages Comparison

### Jenkins Pipeline Stages
1. **Checkout** - Source code retrieval
2. **Build** - Docker image building
3. **Test** - Basic testing
4. **Deploy** - Manual deployment

### GitLab CI Pipeline Stages
1. **Validate** - Project structure validation
2. **Security** - Secret scanning and dependency checks
3. **Build** - Docker image building with registry push
4. **Test** - Comprehensive functional and performance testing
5. **Security-scan** - Container vulnerability scanning
6. **Deploy-staging** - Automated staging deployment
7. **Deploy-production** - Manual production deployment with approval

## Enhanced Features in GitLab CI

### 1. Security Scanning
- Automatic secret detection in source code
- Container vulnerability scanning with Trivy
- Dependency scanning for external libraries

### 2. Multi-Environment Support
- Separate staging and production environments
- Environment-specific variables and configurations
- Deployment approval workflows

### 3. Comprehensive Testing
- Health endpoint verification
- Static asset loading tests
- Performance testing with response time monitoring
- Automatic cleanup of test containers

### 4. Better Observability
- Detailed pipeline logs with emojis for better readability
- Artifact management for reports and dependencies
- Environment URLs for easy access to deployments

## Required GitLab CI/CD Variables

Set these variables in GitLab Project Settings > CI/CD > Variables:

### Staging Environment
```
STAGING_GEMINI_API_KEY (Masked)
```

### Production Environment
```
PRODUCTION_GEMINI_API_KEY (Masked, Protected)
```

### Optional Variables
```
DOCKER_REGISTRY_URL (if using external registry)
NOTIFICATION_WEBHOOK (for deployment notifications)
```

## Deployment Process

### Staging Deployment
- **Trigger**: Automatic on `develop` branch push
- **Manual**: Available for `main` branch
- **Port**: 8080
- **Environment**: staging
- **Verification**: Health check + functional tests

### Production Deployment
- **Trigger**: Manual approval required
- **Branch**: `main` branch only
- **Port**: 80
- **Environment**: production
- **Verification**: Health check + deployment confirmation

## Rollback Strategy

### GitLab CI Rollback
1. Navigate to Deployments > Environments
2. Select the environment (staging/production)
3. Click "Rollback" on the previous successful deployment
4. Confirm rollback action

### Manual Rollback
```bash
# Stop current container
docker stop vietnam-admin-production

# Start previous version
docker run -d --name vietnam-admin-production \
  -p 80:80 \
  -e GEMINI_API_KEY="$PRODUCTION_GEMINI_API_KEY" \
  registry.gitlab.com/your-project/vietnam-admin-restructuring-2025:previous-tag
```

## Monitoring and Alerts

### Pipeline Monitoring
- GitLab CI/CD pipeline status
- Job duration and success rates
- Container registry usage

### Application Monitoring
- Health endpoint monitoring
- Response time tracking
- Error rate monitoring

## Best Practices

### 1. Security
- Never commit API keys or secrets
- Use masked variables for sensitive data
- Enable protected variables for production
- Regular security scanning

### 2. Performance
- Use Docker layer caching
- Optimize Docker image size
- Parallel job execution where possible

### 3. Reliability
- Comprehensive testing at each stage
- Proper error handling and cleanup
- Deployment verification steps
- Rollback procedures

## Troubleshooting

### Common Issues

1. **Docker-in-Docker Issues**
   - Ensure GitLab Runner has privileged mode enabled
   - Check Docker service availability

2. **Registry Authentication**
   - Verify CI_REGISTRY_USER and CI_REGISTRY_PASSWORD are available
   - Check GitLab Container Registry permissions

3. **Environment Variable Issues**
   - Verify variables are set in GitLab project settings
   - Check variable scope (environment-specific)

4. **Deployment Failures**
   - Check container logs: `docker logs container-name`
   - Verify port availability
   - Check environment variable injection

## Support and Documentation

- [GitLab CI/CD Documentation](https://docs.gitlab.com/ee/ci/)
- [GitLab Container Registry](https://docs.gitlab.com/ee/user/packages/container_registry/)
- [GitLab Environments and Deployments](https://docs.gitlab.com/ee/ci/environments/)

## Migration Checklist

- [ ] Configure GitLab CI/CD variables
- [ ] Set up GitLab Runner with Docker executor
- [ ] Test pipeline on feature branch
- [ ] Configure branch protection rules
- [ ] Set up environment URLs
- [ ] Test staging deployment
- [ ] Test production deployment (with approval)
- [ ] Configure monitoring and alerts
- [ ] Update team documentation
- [ ] Archive Jenkins pipeline (optional)

---

**Note**: This migration provides enhanced security, better integration, and improved deployment workflows compared to the previous Jenkins setup.

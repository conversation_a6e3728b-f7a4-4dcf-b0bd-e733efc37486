# Harbor Container Registry Setup for GitLab CI/CD

This document provides step-by-step instructions for configuring GitLab CI/CD variables to work with your Harbor container registry at `harbor.veasy.vn`.

## Required GitLab CI/CD Variables

### 1. Harbor Registry Credentials

In your GitLab project, navigate to **Settings > CI/CD > Variables** and add the following variables:

#### Harbor Authentication Variables
- **Variable Name**: `HARBOR_USERNAME`
  - **Value**: Your Harbor username (e.g., `admin` or your Harbor user)
  - **Type**: Variable
  - **Environment**: All
  - **Protected**: ✅ (Recommended)
  - **Masked**: ✅ (Recommended)

- **Variable Name**: `HARBOR_PASSWORD`
  - **Value**: Your Harbor password or access token
  - **Type**: Variable
  - **Environment**: All
  - **Protected**: ✅ (Recommended)
  - **Masked**: ✅ (Recommended)

### 2. Production Environment Variables

- **Variable Name**: `PRODUCTION_GEMINI_API_KEY`
  - **Value**: Your production Gemini API key
  - **Type**: Variable
  - **Environment**: production
  - **Protected**: ✅
  - **Masked**: ✅

## Harbor Project Setup

### 1. Create Harbor Project

1. Login to Harbor at `https://harbor.veasy.vn`
2. Click **"+ NEW PROJECT"**
3. Fill in the project details:
   - **Project Name**: `vietnam-admin`
   - **Access Level**: Private (Recommended)
   - **Storage Quota**: Set as needed (e.g., 10GB)
4. Click **"OK"** to create the project

### 2. Configure Project Settings

1. Navigate to your project `vietnam-admin`
2. Go to **"Configuration"** tab
3. Configure the following settings:
   - **Public**: Unchecked (for private access)
   - **Enable content trust**: Checked (for security)
   - **Prevent vulnerable images from running**: Checked
   - **Automatically scan images on push**: Checked

### 3. Set Up Robot Account (Optional but Recommended)

For better security, create a robot account instead of using your personal credentials:

1. In your Harbor project, go to **"Robot Accounts"** tab
2. Click **"+ NEW ROBOT ACCOUNT"**
3. Configure:
   - **Name**: `gitlab-ci`
   - **Description**: `GitLab CI/CD automation`
   - **Expiration time**: Set appropriate duration
   - **Permissions**: 
     - ✅ Push repository
     - ✅ Pull repository
     - ✅ Delete repository (if needed)
4. Copy the generated token and use it as `HARBOR_PASSWORD`
5. Use the robot account name (e.g., `robot$gitlab-ci`) as `HARBOR_USERNAME`

## Pipeline Configuration Verification

### Current Pipeline Settings

The pipeline is configured with the following Harbor integration:

```yaml
# Harbor Registry configuration
HARBOR_REGISTRY: "harbor.veasy.vn"
HARBOR_PROJECT: "vietnam-admin"

# Image naming with Harbor registry
DOCKER_IMAGE: "${HARBOR_REGISTRY}/${HARBOR_PROJECT}/${APP_NAME}:${APP_VERSION}"
DOCKER_IMAGE_LATEST: "${HARBOR_REGISTRY}/${HARBOR_PROJECT}/${APP_NAME}:latest"
DOCKER_IMAGE_STAGING: "${HARBOR_REGISTRY}/${HARBOR_PROJECT}/${APP_NAME}:staging"
```

### Expected Image Names

Based on the current configuration, your images will be stored as:
- **Production**: `harbor.veasy.vn/vietnam-admin/vietnam-admin-restructuring-2025:abc1234`
- **Latest**: `harbor.veasy.vn/vietnam-admin/vietnam-admin-restructuring-2025:latest`
- **Staging**: `harbor.veasy.vn/vietnam-admin/vietnam-admin-restructuring-2025:staging`

## Testing Harbor Integration

### 1. Manual Docker Login Test

Test your Harbor credentials locally:

```bash
# Test login to Harbor
docker login harbor.veasy.vn -u <HARBOR_USERNAME>

# Test pulling a public image (if available)
docker pull harbor.veasy.vn/library/hello-world:latest
```

### 2. Pipeline Test

1. Commit a small change to trigger the pipeline
2. Monitor the `build:docker` job logs for:
   - ✅ Successful Harbor authentication
   - ✅ Successful image push to Harbor
   - ✅ Image availability in Harbor UI

### 3. Verify in Harbor UI

1. Login to Harbor at `https://harbor.veasy.vn`
2. Navigate to project `vietnam-admin`
3. Check the **"Repositories"** tab
4. Verify your images are listed with correct tags

## Customization Options

### 1. Change Harbor Project Name

If you want to use a different project name, update the variable in `.gitlab-ci.yml`:

```yaml
HARBOR_PROJECT: "your-custom-project-name"
```

### 2. Add Environment-Specific Projects

For different environments, you can modify the pipeline to use different projects:

```yaml
# Development images
HARBOR_PROJECT_DEV: "vietnam-admin-dev"
# Production images  
HARBOR_PROJECT_PROD: "vietnam-admin-prod"
```

### 3. Custom Image Naming

Modify image names by updating these variables:

```yaml
DOCKER_IMAGE: "${HARBOR_REGISTRY}/${HARBOR_PROJECT}/custom-name:${APP_VERSION}"
```

## Security Best Practices

### 1. Harbor Security Settings

- ✅ Enable vulnerability scanning
- ✅ Use robot accounts for CI/CD
- ✅ Enable image signing (Notary)
- ✅ Configure retention policies
- ✅ Enable audit logging

### 2. GitLab Variable Security

- ✅ Mark sensitive variables as "Protected" and "Masked"
- ✅ Use environment-specific variables
- ✅ Regularly rotate Harbor credentials
- ✅ Monitor variable usage in CI/CD logs

### 3. Network Security

- ✅ Configure Harbor behind HTTPS
- ✅ Use private networks where possible
- ✅ Implement proper firewall rules
- ✅ Monitor Harbor access logs

## Troubleshooting

### Common Issues

1. **Authentication Failed**
   - Verify Harbor credentials in GitLab variables
   - Check Harbor user permissions
   - Ensure Harbor is accessible from GitLab runners

2. **Project Not Found**
   - Verify Harbor project exists
   - Check project name in pipeline variables
   - Ensure user has access to the project

3. **Push Permission Denied**
   - Verify user has push permissions
   - Check robot account permissions
   - Ensure project allows pushes

### Debug Commands

Add these to your pipeline for debugging:

```bash
# Test Harbor connectivity
curl -v https://harbor.veasy.vn/api/v2.0/projects

# Check Docker login status
docker info | grep Registry

# List available images
docker images | grep harbor.veasy.vn
```

## Next Steps

1. ✅ Set up Harbor credentials in GitLab CI/CD variables
2. ✅ Create the `vietnam-admin` project in Harbor
3. ✅ Test the pipeline with a small commit
4. ✅ Verify images appear in Harbor UI
5. ✅ Configure Harbor security settings
6. ✅ Set up retention policies for image cleanup

For additional Harbor features like vulnerability scanning, image replication, or advanced security policies, refer to the [Harbor Documentation](https://goharbor.io/docs/).
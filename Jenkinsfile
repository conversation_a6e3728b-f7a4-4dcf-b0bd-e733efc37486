#!/usr/bin/env groovy

/**
 * Jenkins Pipeline for Vietnam Administrative Restructuring 2025
 * 
 * This pipeline handles the complete CI/CD process including:
 * - Code checkout and validation
 * - Security scanning
 * - Docker image building
 * - Testing and quality assurance
 * - Deployment to different environments
 * 
 * Security Best Practices:
 * - No hardcoded secrets or credentials
 * - Environment variables managed through <PERSON> credentials
 * - Secure Docker image scanning
 * - Proper artifact management
 */

pipeline {
    agent any
    
    // Environment variables (use <PERSON> credentials for sensitive data)
    environment {
        // Application configuration
        APP_NAME = 'vietnam-admin-restructuring-2025'
        APP_VERSION = "${env.BUILD_NUMBER}"
        
        // Docker configuration
        DOCKER_REGISTRY = credentials('docker-registry-url') // Configure in Jenkins
        DOCKER_REPO = "${DOCKER_REGISTRY}/${APP_NAME}"
        DOCKER_TAG = "${APP_VERSION}"
        
        // Deployment configuration
        STAGING_URL = credentials('staging-url') // Configure in Jenkins
        PRODUCTION_URL = credentials('production-url') // Configure in Jenkins
        
        // Security scanning
        TRIVY_CACHE_DIR = "${WORKSPACE}/.trivy-cache"
    }
    
    // Build triggers
    triggers {
        // Poll SCM every 5 minutes for changes
        pollSCM('H/5 * * * *')
        
        // Build daily at 2 AM for security scans
        cron('0 2 * * *')
    }
    
    // Pipeline options
    options {
        // Keep only last 10 builds
        buildDiscarder(logRotator(numToKeepStr: '10'))
        
        // Timeout after 30 minutes
        timeout(time: 30, unit: 'MINUTES')
        
        // Disable concurrent builds
        disableConcurrentBuilds()
        
        // Add timestamps to console output
        timestamps()
    }
    
    stages {
        stage('Checkout & Validation') {
            steps {
                script {
                    echo "🚀 Starting build for ${APP_NAME} version ${APP_VERSION}"
                    
                    // Checkout code
                    checkout scm
                    
                    // Validate required files exist
                    sh '''
                        echo "📋 Validating project structure..."
                        
                        # Check required files
                        required_files=(
                            "index.html"
                            "Dockerfile"
                            "docker-compose.yml"
                            ".gitignore"
                            ".dockerignore"
                        )
                        
                        for file in "${required_files[@]}"; do
                            if [[ ! -f "$file" ]]; then
                                echo "❌ Required file missing: $file"
                                exit 1
                            fi
                        done
                        
                        # Check required directories
                        required_dirs=(
                            "assets/css"
                            "assets/js"
                            "config"
                        )
                        
                        for dir in "${required_dirs[@]}"; do
                            if [[ ! -d "$dir" ]]; then
                                echo "❌ Required directory missing: $dir"
                                exit 1
                            fi
                        done
                        
                        echo "✅ Project structure validation passed"
                    '''
                }
            }
        }
        
        stage('Security Scan - Source Code') {
            parallel {
                stage('Secret Detection') {
                    steps {
                        script {
                            echo "🔍 Scanning for secrets in source code..."
                            
                            sh '''
                                # Simple secret detection (replace with proper tool like truffleHog)
                                echo "Checking for potential secrets..."
                                
                                # Check for common secret patterns
                                if grep -r -i "api[_-]key.*=" . --exclude-dir=.git --exclude="Jenkinsfile" | grep -v ".example" | grep -v "your_api_key_here"; then
                                    echo "⚠️  Potential API keys found in source code"
                                    echo "Please review and ensure no real API keys are committed"
                                fi
                                
                                # Check for hardcoded passwords
                                if grep -r -i "password.*=" . --exclude-dir=.git --exclude="Jenkinsfile" | grep -v "your_password_here"; then
                                    echo "⚠️  Potential passwords found in source code"
                                fi
                                
                                echo "✅ Secret detection completed"
                            '''
                        }
                    }
                }
                
                stage('Dependency Check') {
                    steps {
                        script {
                            echo "📦 Checking dependencies for vulnerabilities..."
                            
                            sh '''
                                # Check external dependencies
                                echo "Analyzing external dependencies..."
                                
                                # Extract CDN dependencies from HTML
                                if grep -o 'https://cdn\.[^"]*' index.html > dependencies.txt; then
                                    echo "📋 External dependencies found:"
                                    cat dependencies.txt
                                else
                                    echo "ℹ️  No external CDN dependencies detected"
                                fi
                                
                                echo "✅ Dependency check completed"
                            '''
                        }
                    }
                }
            }
        }
        
        stage('Build Docker Image') {
            steps {
                script {
                    echo "🐳 Building Docker image..."
                    
                    // Build Docker image
                    def dockerImage = docker.build("${DOCKER_REPO}:${DOCKER_TAG}")
                    
                    // Tag as latest for current branch
                    if (env.BRANCH_NAME == 'main') {
                        dockerImage.tag('latest')
                    }
                    
                    // Store image ID for later use
                    env.DOCKER_IMAGE_ID = dockerImage.id
                    
                    echo "✅ Docker image built successfully: ${DOCKER_REPO}:${DOCKER_TAG}"
                }
            }
        }
        
        stage('Security Scan - Docker Image') {
            steps {
                script {
                    echo "🔒 Scanning Docker image for vulnerabilities..."
                    
                    sh '''
                        # Create cache directory for Trivy
                        mkdir -p ${TRIVY_CACHE_DIR}
                        
                        # Install Trivy if not available (adjust for your environment)
                        if ! command -v trivy &> /dev/null; then
                            echo "📥 Installing Trivy scanner..."
                            # Add Trivy installation commands here based on your Jenkins environment
                            echo "⚠️  Trivy not installed. Please install Trivy for security scanning."
                        else
                            echo "🔍 Running Trivy security scan..."
                            trivy image --cache-dir ${TRIVY_CACHE_DIR} --format json --output trivy-report.json ${DOCKER_REPO}:${DOCKER_TAG}
                            
                            # Check for HIGH and CRITICAL vulnerabilities
                            if trivy image --cache-dir ${TRIVY_CACHE_DIR} --severity HIGH,CRITICAL --exit-code 1 ${DOCKER_REPO}:${DOCKER_TAG}; then
                                echo "✅ No critical vulnerabilities found"
                            else
                                echo "⚠️  Critical vulnerabilities detected. Review trivy-report.json"
                                # Don't fail the build for now, just warn
                            fi
                        fi
                    '''
                }
            }
            post {
                always {
                    // Archive security scan results
                    archiveArtifacts artifacts: 'trivy-report.json', allowEmptyArchive: true
                }
            }
        }
        
        stage('Test') {
            parallel {
                stage('Functional Tests') {
                    steps {
                        script {
                            echo "🧪 Running functional tests..."
                            
                            sh '''
                                # Start container for testing
                                docker run -d --name test-container-${BUILD_NUMBER} -p 8080:80 \
                                    -e GEMINI_API_KEY="test_key" \
                                    ${DOCKER_REPO}:${DOCKER_TAG}
                                
                                # Wait for container to be ready
                                sleep 10
                                
                                # Basic health check
                                if curl -f http://localhost:8080/health; then
                                    echo "✅ Health check passed"
                                else
                                    echo "❌ Health check failed"
                                    exit 1
                                fi
                                
                                # Test main page loads
                                if curl -f -s http://localhost:8080/ | grep -q "Tái cấu trúc"; then
                                    echo "✅ Main page loads correctly"
                                else
                                    echo "❌ Main page test failed"
                                    exit 1
                                fi
                                
                                # Test static assets
                                if curl -f http://localhost:8080/assets/css/main.css > /dev/null; then
                                    echo "✅ CSS assets load correctly"
                                else
                                    echo "❌ CSS assets test failed"
                                    exit 1
                                fi
                                
                                if curl -f http://localhost:8080/assets/js/app.js > /dev/null; then
                                    echo "✅ JavaScript assets load correctly"
                                else
                                    echo "❌ JavaScript assets test failed"
                                    exit 1
                                fi
                                
                                echo "✅ All functional tests passed"
                            '''
                        }
                    }
                    post {
                        always {
                            // Clean up test container
                            sh '''
                                docker stop test-container-${BUILD_NUMBER} || true
                                docker rm test-container-${BUILD_NUMBER} || true
                            '''
                        }
                    }
                }
                
                stage('Performance Tests') {
                    steps {
                        script {
                            echo "⚡ Running performance tests..."
                            
                            sh '''
                                # Basic performance test using curl
                                echo "Testing response times..."
                                
                                # Test main page response time
                                response_time=$(curl -o /dev/null -s -w "%{time_total}" http://localhost:8080/)
                                echo "Main page response time: ${response_time}s"
                                
                                # Check if response time is acceptable (< 2 seconds)
                                if (( $(echo "${response_time} < 2.0" | bc -l) )); then
                                    echo "✅ Performance test passed"
                                else
                                    echo "⚠️  Performance test warning: slow response time"
                                fi
                            '''
                        }
                    }
                }
            }
        }
        
        stage('Push to Registry') {
            when {
                anyOf {
                    branch 'main'
                    branch 'develop'
                    buildingTag()
                }
            }
            steps {
                script {
                    echo "📤 Pushing Docker image to registry..."
                    
                    // Push to Docker registry
                    docker.withRegistry("https://${DOCKER_REGISTRY}", 'docker-registry-credentials') {
                        def image = docker.image("${DOCKER_REPO}:${DOCKER_TAG}")
                        image.push()
                        
                        if (env.BRANCH_NAME == 'main') {
                            image.push('latest')
                        }
                    }
                    
                    echo "✅ Docker image pushed successfully"
                }
            }
        }
        
        stage('Deploy to Staging') {
            when {
                branch 'develop'
            }
            steps {
                script {
                    echo "🚀 Deploying to staging environment..."
                    
                    // Deploy to staging (customize based on your infrastructure)
                    sh '''
                        echo "Deploying to staging..."
                        # Add your staging deployment commands here
                        # Example: kubectl, docker-compose, or other deployment tools
                        
                        echo "✅ Staging deployment completed"
                        echo "🌐 Staging URL: ${STAGING_URL}"
                    '''
                }
            }
        }
        
        stage('Deploy to Production') {
            when {
                branch 'main'
            }
            steps {
                script {
                    // Manual approval for production deployment
                    input message: 'Deploy to production?', ok: 'Deploy',
                          submitterParameter: 'DEPLOYER'
                    
                    echo "🚀 Deploying to production environment..."
                    echo "👤 Deployed by: ${env.DEPLOYER}"
                    
                    sh '''
                        echo "Deploying to production..."
                        # Add your production deployment commands here
                        # Example: kubectl, docker-compose, or other deployment tools
                        
                        echo "✅ Production deployment completed"
                        echo "🌐 Production URL: ${PRODUCTION_URL}"
                    '''
                }
            }
        }
    }
    
    post {
        always {
            // Clean up workspace
            cleanWs()
        }
        
        success {
            echo "✅ Pipeline completed successfully!"
            
            // Send success notification (configure based on your needs)
            // emailext (
            //     subject: "✅ Build Success: ${APP_NAME} #${BUILD_NUMBER}",
            //     body: "Build completed successfully for ${APP_NAME} version ${APP_VERSION}",
            //     to: "${env.CHANGE_AUTHOR_EMAIL}"
            // )
        }
        
        failure {
            echo "❌ Pipeline failed!"
            
            // Send failure notification
            // emailext (
            //     subject: "❌ Build Failed: ${APP_NAME} #${BUILD_NUMBER}",
            //     body: "Build failed for ${APP_NAME}. Please check the logs.",
            //     to: "${env.CHANGE_AUTHOR_EMAIL}"
            // )
        }
        
        unstable {
            echo "⚠️ Pipeline completed with warnings!"
        }
    }
}

# Vietnam Administrative Restructuring 2025 - Development Nginx Configuration
# This configuration is optimized for development with live reload capabilities

server {
    listen 80;
    listen [::]:80;
    server_name localhost;

    # Development-specific settings
    access_log /var/log/nginx/access.log;
    error_log /var/log/nginx/error.log debug;

    # Root directory
    root /usr/share/nginx/html;
    index index.html;

    # Disable caching for development
    add_header Cache-Control "no-cache, no-store, must-revalidate" always;
    add_header Pragma "no-cache" always;
    add_header Expires "0" always;

    # CORS headers for development
    add_header Access-Control-Allow-Origin "*" always;
    add_header Access-Control-Allow-Methods "GET, POST, OPTIONS" always;
    add_header Access-Control-Allow-Headers "Origin, X-Requested-With, Content-Type, Accept, Authorization" always;

    # Security headers (relaxed for development)
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;

    # Main application
    location / {
        try_files $uri $uri/ /index.html;
    }

    # Allow access to all files for development
    location ~* \.(css|js|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot|html|json|txt|md)$ {
        try_files $uri =404;
    }

    # Health check endpoint
    location /health {
        access_log off;
        return 200 "healthy (development)\n";
        add_header Content-Type text/plain;
    }

    # Development info endpoint
    location /dev-info {
        access_log off;
        return 200 "Development server running\nTime: $time_iso8601\nHost: $hostname\n";
        add_header Content-Type text/plain;
    }
}

# GitLab CI/CD Variables Setup Guide

## Overview

This guide explains how to configure GitLab CI/CD variables for the Vietnam Administrative Restructuring 2025 project. Proper variable configuration is essential for secure and successful deployments.

## Accessing GitLab CI/CD Variables

1. Navigate to your GitLab project
2. Go to **Settings** > **CI/CD**
3. Expand the **Variables** section
4. Click **Add variable** to create new variables

## Required Variables

### 1. Staging Environment Variables

#### STAGING_GEMINI_API_KEY
- **Type**: Variable
- **Environment scope**: staging
- **Flags**: 
  - ✅ Masked (hide value in job logs)
  - ❌ Protected (not needed for staging)
- **Value**: Your Gemini API key for staging environment
- **Description**: API key for Gemini AI service in staging

### 2. Production Environment Variables

#### PRODUCTION_GEMINI_API_KEY
- **Type**: Variable
- **Environment scope**: production
- **Flags**: 
  - ✅ Masked (hide value in job logs)
  - ✅ Protected (only available on protected branches)
- **Value**: Your Gemini API key for production environment
- **Description**: API key for Gemini AI service in production

## Optional Variables

### 3. Notification Variables (Optional)

#### SLACK_WEBHOOK_URL
- **Type**: Variable
- **Environment scope**: All environments
- **Flags**: 
  - ✅ Masked
  - ❌ Protected
- **Value**: Slack webhook URL for deployment notifications
- **Description**: Webhook for sending deployment notifications to Slack

#### TEAMS_WEBHOOK_URL
- **Type**: Variable
- **Environment scope**: All environments
- **Flags**: 
  - ✅ Masked
  - ❌ Protected
- **Value**: Microsoft Teams webhook URL
- **Description**: Webhook for sending deployment notifications to Teams

### 4. Custom Configuration Variables (Optional)

#### CUSTOM_DOMAIN_STAGING
- **Type**: Variable
- **Environment scope**: staging
- **Flags**: 
  - ❌ Masked
  - ❌ Protected
- **Value**: staging.vietnam-admin-restructuring.local
- **Description**: Custom domain for staging environment

#### CUSTOM_DOMAIN_PRODUCTION
- **Type**: Variable
- **Environment scope**: production
- **Flags**: 
  - ❌ Masked
  - ✅ Protected
- **Value**: vietnam-admin-restructuring.gov.vn
- **Description**: Custom domain for production environment

## Built-in GitLab Variables Used

The pipeline automatically uses these GitLab-provided variables:

| Variable | Description | Example Value |
|----------|-------------|---------------|
| `CI_REGISTRY` | GitLab Container Registry URL | registry.gitlab.com |
| `CI_REGISTRY_IMAGE` | Full image path | registry.gitlab.com/group/project |
| `CI_REGISTRY_USER` | Registry username | gitlab-ci-token |
| `CI_REGISTRY_PASSWORD` | Registry password | (auto-generated) |
| `CI_PIPELINE_ID` | Unique pipeline identifier | 123456 |
| `CI_COMMIT_BRANCH` | Current branch name | main |
| `CI_COMMIT_REF_NAME` | Branch or tag name | main |
| `CI_DEFAULT_BRANCH` | Default branch name | main |
| `GITLAB_USER_NAME` | User who triggered pipeline | John Doe |
| `GITLAB_USER_EMAIL` | User email | <EMAIL> |

## Variable Configuration Examples

### Step-by-Step Variable Creation

#### Creating STAGING_GEMINI_API_KEY

1. **Add variable**
2. **Key**: `STAGING_GEMINI_API_KEY`
3. **Value**: `AIzaSyBcvOTZxypU5SwzPqUD2NgxDbhgZ6VenGo` (your actual key)
4. **Type**: Variable
5. **Environment scope**: `staging`
6. **Flags**: 
   - ✅ Mask variable
   - ❌ Protect variable
7. **Description**: `Gemini API key for staging environment`
8. Click **Add variable**

#### Creating PRODUCTION_GEMINI_API_KEY

1. **Add variable**
2. **Key**: `PRODUCTION_GEMINI_API_KEY`
3. **Value**: `AIzaSyBcvOTZxypU5SwzPqUD2NgxDbhgZ6VenGo` (your actual production key)
4. **Type**: Variable
5. **Environment scope**: `production`
6. **Flags**: 
   - ✅ Mask variable
   - ✅ Protect variable
7. **Description**: `Gemini API key for production environment`
8. Click **Add variable**

## Environment Scopes

### Understanding Environment Scopes

- **All environments**: Variable available in all jobs
- **staging**: Variable only available in staging environment jobs
- **production**: Variable only available in production environment jobs
- **develop**: Variable only available when deploying from develop branch

### Environment Scope Priority

GitLab uses the most specific scope match:
1. Exact environment match (e.g., `production`)
2. Wildcard match (e.g., `prod*`)
3. All environments (`*`)

## Security Best Practices

### 1. Masked Variables
- Always mask sensitive data (API keys, passwords, tokens)
- Masked variables are hidden in job logs
- Values with fewer than 8 characters cannot be masked

### 2. Protected Variables
- Use for production-only variables
- Only available on protected branches (main, master)
- Prevents accidental exposure in feature branches

### 3. Variable Naming
- Use descriptive names with environment prefixes
- Follow consistent naming conventions
- Include environment in variable name for clarity

### 4. Regular Rotation
- Rotate API keys regularly
- Update variables when keys change
- Monitor key usage and access

## Validation Script

Use this script to validate your variable configuration:

```bash
#!/bin/bash
# validate-variables.sh

echo "🔍 Validating GitLab CI/CD Variables..."

# Check if required variables are set
required_vars=(
    "STAGING_GEMINI_API_KEY"
    "PRODUCTION_GEMINI_API_KEY"
)

for var in "${required_vars[@]}"; do
    if [[ -z "${!var}" ]]; then
        echo "❌ Missing required variable: $var"
        exit 1
    else
        echo "✅ Variable $var is set"
    fi
done

echo "✅ All required variables are configured"
```

## Troubleshooting

### Common Issues

#### 1. Variable Not Available in Job
**Problem**: Variable shows as empty in pipeline
**Solutions**:
- Check environment scope matches job environment
- Verify variable name spelling
- Ensure variable is not protected when used in non-protected branch

#### 2. Masked Variable Still Visible
**Problem**: Sensitive data appears in logs
**Solutions**:
- Ensure variable is marked as masked
- Check if value meets masking requirements (8+ characters)
- Verify no echo statements output the variable directly

#### 3. Protected Variable Not Working
**Problem**: Variable not available in production job
**Solutions**:
- Ensure branch is protected in repository settings
- Verify variable is marked as protected
- Check if job runs on protected branch

### Debugging Variables

Add this to your pipeline for debugging (remove before production):

```yaml
debug:variables:
  stage: validate
  script:
    - echo "Pipeline ID: $CI_PIPELINE_ID"
    - echo "Branch: $CI_COMMIT_BRANCH"
    - echo "Environment: $CI_ENVIRONMENT_NAME"
    - echo "Registry: $CI_REGISTRY"
    - echo "Image: $CI_REGISTRY_IMAGE"
    # Never echo sensitive variables!
  only:
    - develop
```

## Variable Management Checklist

- [ ] STAGING_GEMINI_API_KEY configured and masked
- [ ] PRODUCTION_GEMINI_API_KEY configured, masked, and protected
- [ ] Environment scopes properly set
- [ ] Variable descriptions added
- [ ] Security flags (masked/protected) properly configured
- [ ] Variables tested in pipeline
- [ ] Documentation updated
- [ ] Team members informed of variable names

## Support

For additional help with GitLab CI/CD variables:
- [GitLab CI/CD Variables Documentation](https://docs.gitlab.com/ee/ci/variables/)
- [GitLab Environments Documentation](https://docs.gitlab.com/ee/ci/environments/)
- [GitLab Security Best Practices](https://docs.gitlab.com/ee/ci/variables/#cicd-variable-security)

---

**Important**: Never commit actual API keys or sensitive data to your repository. Always use GitLab CI/CD variables for sensitive information.

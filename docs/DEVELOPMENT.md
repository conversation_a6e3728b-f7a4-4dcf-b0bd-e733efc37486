# Development Guide

This guide provides detailed information for developers working on the Vietnam Administrative Restructuring 2025 application.

## Architecture Overview

The application follows a modular, component-based architecture:

```
┌─────────────────┐
│    index.html   │  ← Entry point
└─────────────────┘
         │
         ▼
┌─────────────────┐
│     app.js      │  ← Main controller
└─────────────────┘
         │
    ┌────┴────┐
    ▼         ▼
┌─────────┐ ┌─────────┐
│charts.js│ │modal.js │  ← Feature modules
└─────────┘ └─────────┘
    │         │
    └────┬────┘
         ▼
┌─────────────────┐
│     api.js      │  ← External services
└─────────────────┘
         │
         ▼
┌─────────────────┐
│    utils.js     │  ← Shared utilities
└─────────────────┘
```

## Module Responsibilities

### app.js - Main Application Controller
- Initializes all modules
- Coordinates module interactions
- Handles global error management
- Manages application lifecycle

### charts.js - Data Visualization
- Creates and manages Chart.js instances
- Handles chart configuration and data
- Provides chart update and destroy methods

### modal.js - User Interface
- Manages modal display and interactions
- Handles loading states
- Coordinates with API module for data fetching

### api.js - External Services
- Manages Gemini AI API integration
- Handles request/response processing
- Provides error handling and retry logic

### utils.js - Shared Utilities
- Common helper functions
- DOM manipulation utilities
- Data formatting functions

## Development Setup

### Prerequisites
- A modern web browser
- A local web server (Python, Node.js, PHP, etc.)
- Text editor or IDE
- Gemini AI API key (for AI features)

### Local Development

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd vietnam-admin-restructuring-2025
   ```

2. **Set up environment**
   ```bash
   cp .env.example .env
   # Edit .env with your API key
   ```

3. **Start local server**
   ```bash
   # Using Python
   python -m http.server 8000
   
   # Using Node.js
   npx serve .
   
   # Using PHP
   php -S localhost:8000
   ```

4. **Open in browser**
   ```
   http://localhost:8000
   ```

## Code Style and Standards

### JavaScript Standards

- Use ES6+ features where supported
- Follow consistent naming conventions:
  - `camelCase` for variables and functions
  - `PascalCase` for classes
  - `UPPER_CASE` for constants
- Add JSDoc comments for functions and classes
- Handle errors gracefully with try-catch blocks

### CSS Standards

- Use Tailwind CSS utilities where possible
- Custom CSS should be organized by purpose:
  - `main.css` - Base styles and layout
  - `components.css` - Component-specific styles
  - `animations.css` - Animations and transitions
- Use meaningful class names
- Follow mobile-first responsive design

### HTML Standards

- Use semantic HTML5 elements
- Include proper meta tags
- Add comments for major sections
- Ensure accessibility with proper ARIA labels

## Adding New Features

### 1. Create a New Module

```javascript
// assets/js/new-feature.js
class NewFeature {
    constructor() {
        this.initialized = false;
    }

    init() {
        // Initialize the feature
        this.initialized = true;
    }

    // Feature methods...
}

// Export for use
if (typeof module !== 'undefined' && module.exports) {
    module.exports = NewFeature;
} else {
    window.NewFeature = NewFeature;
}
```

### 2. Update the Main Application

```javascript
// In app.js
initializeNewFeature() {
    try {
        this.newFeature = new NewFeature();
        this.newFeature.init();
        console.log('New feature initialized successfully');
    } catch (error) {
        console.error('Failed to initialize new feature:', error);
        throw new Error('New feature initialization failed');
    }
}
```

### 3. Add to HTML

```html
<!-- In index.html -->
<script src="assets/js/new-feature.js"></script>
```

## Testing

### Manual Testing Checklist

- [ ] Application loads without errors
- [ ] Charts render correctly
- [ ] Modal opens and closes properly
- [ ] AI analysis works (with valid API key)
- [ ] Responsive design works on different screen sizes
- [ ] All buttons and interactions function correctly

### Browser Testing

Test in the following browsers:
- Chrome (latest)
- Firefox (latest)
- Safari (latest)
- Edge (latest)

### Error Testing

- Test with invalid API key
- Test with network disconnection
- Test with malformed data
- Test browser console for errors

## Performance Considerations

### Loading Performance
- External dependencies are loaded from CDN
- CSS and JS files are separate for better caching
- Images should be optimized (if added)

### Runtime Performance
- Charts are created once and reused
- Event listeners are properly managed
- Memory leaks are prevented by cleanup methods

### API Performance
- API calls are made only when needed
- Error handling prevents infinite retry loops
- Consider implementing request caching

## Debugging

### Common Issues

1. **Module not found errors**
   - Check script loading order in HTML
   - Verify file paths are correct
   - Ensure web server is serving files properly

2. **API errors**
   - Check API key configuration
   - Verify network connectivity
   - Check browser console for detailed errors

3. **Chart rendering issues**
   - Ensure Chart.js is loaded before chart modules
   - Check canvas elements exist in DOM
   - Verify data format is correct

### Debugging Tools

- Browser Developer Tools
- Console logging (use `console.log`, `console.error`)
- Network tab for API call inspection
- Application tab for local storage/environment variables

## Deployment

### Development Deployment
- Serve files through any web server
- Ensure environment variables are set
- Test all functionality

### Production Deployment
- Use a proper web server (Apache, Nginx, etc.)
- Implement backend API proxy for security
- Set up proper environment variable management
- Enable HTTPS
- Implement proper error logging

## Contributing

### Pull Request Process

1. Create a feature branch
2. Make your changes
3. Test thoroughly
4. Update documentation if needed
5. Submit pull request with clear description

### Code Review Checklist

- [ ] Code follows style guidelines
- [ ] All functions have proper error handling
- [ ] New features are properly documented
- [ ] No console errors in browser
- [ ] Responsive design is maintained
- [ ] API integration works correctly

## Troubleshooting

### Environment Issues
- Verify `.env` file exists and has correct format
- Check file permissions
- Ensure web server can access all files

### Module Loading Issues
- Check script order in HTML
- Verify all dependencies are loaded
- Check for JavaScript syntax errors

### API Integration Issues
- Verify API key is valid
- Check network connectivity
- Review API documentation for changes

For additional help, check the browser console for detailed error messages and refer to the API setup guide.

# API Setup Guide

This guide explains how to set up the Gemini AI API for the Vietnam Administrative Restructuring 2025 application.

## Getting a Gemini AI API Key

### Step 1: Access Google AI Studio

1. Go to [Google AI Studio](https://makersuite.google.com/app/apikey)
2. Sign in with your Google account
3. If you don't have a Google account, create one first

### Step 2: Create an API Key

1. Click on "Create API Key"
2. Choose your Google Cloud project (or create a new one)
3. Copy the generated API key
4. Store it securely - you won't be able to see it again

### Step 3: Configure the Application

#### Method 1: Using .env File (Recommended)

1. Copy `.env.example` to `.env`:
   ```bash
   cp .env.example .env
   ```

2. Edit the `.env` file and add your API key:
   ```env
   GEMINI_API_KEY=your_actual_api_key_here
   ```

#### Method 2: Direct Configuration (Development Only)

Add the API key directly in your HTML before the application scripts:

```html
<script>
    window.GEMINI_API_KEY = 'your_actual_api_key_here';
</script>
```

## API Usage and Limits

### Free Tier Limits

The Gemini AI API offers a generous free tier:
- **Rate Limit**: 60 requests per minute
- **Daily Limit**: 1,500 requests per day
- **Monthly Limit**: No monthly limit on free tier

### Best Practices

1. **Cache Results**: The application doesn't currently cache API responses, but you could implement caching to reduce API calls
2. **Error Handling**: The application includes comprehensive error handling for API failures
3. **Rate Limiting**: Be mindful of the rate limits when testing

## Troubleshooting

### Common Issues

#### "API key not configured" Error

**Cause**: The API key is not properly set or loaded.

**Solutions**:
1. Verify your `.env` file contains the correct API key
2. Ensure you're serving the application through a web server (not opening the HTML file directly)
3. Check the browser console for any loading errors

#### "Invalid API key" Error

**Cause**: The API key is incorrect or has been revoked.

**Solutions**:
1. Verify you copied the API key correctly
2. Check if the API key is still active in Google AI Studio
3. Generate a new API key if necessary

#### "Rate limit exceeded" Error

**Cause**: You've exceeded the API rate limits.

**Solutions**:
1. Wait for the rate limit to reset (1 minute for per-minute limits)
2. Implement request throttling in your application
3. Consider upgrading to a paid plan for higher limits

#### Network/CORS Errors

**Cause**: Browser security restrictions or network issues.

**Solutions**:
1. Ensure you're serving the application through a web server
2. Check your internet connection
3. Verify the API endpoint is accessible

### Testing Your Setup

1. Open the application in your browser
2. Click any "✨ Phân tích Tác động" button
3. If configured correctly, you should see an AI-generated analysis
4. Check the browser console for any error messages

## Security Considerations

### Development vs Production

#### Development (Current Setup)
- API key is loaded client-side
- Suitable for local development and demos
- **Not recommended for production**

#### Production Recommendations
1. **Backend Proxy**: Create a backend service that handles API calls
2. **Environment Variables**: Use server-side environment variables
3. **Authentication**: Implement user authentication
4. **Rate Limiting**: Add application-level rate limiting

### API Key Security

⚠️ **Never commit API keys to version control**

1. Add `.env` to your `.gitignore` file
2. Use different API keys for development and production
3. Regularly rotate your API keys
4. Monitor API usage in Google Cloud Console

## Alternative Configurations

### Using a Backend Proxy

For production applications, consider creating a backend endpoint:

```javascript
// Instead of calling Gemini directly:
const response = await fetch('/api/analyze', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ provinces: 'Hà Giang và Tuyên Quang' })
});
```

### Environment-Specific Configuration

You can set different configurations for different environments:

```javascript
const CONFIG = {
    development: {
        apiKey: process.env.GEMINI_API_KEY_DEV,
        debug: true
    },
    production: {
        apiKey: process.env.GEMINI_API_KEY_PROD,
        debug: false
    }
};
```

## Support

If you encounter issues with API setup:

1. Check the [Google AI Studio documentation](https://ai.google.dev/docs)
2. Review the browser console for error messages
3. Verify your API key permissions and quotas
4. Test with a simple API call outside the application

# Vietnam Administrative Restructuring 2025 - Docker Ignore File
# This file specifies files and directories that should not be copied to the Docker image

# ===========================
# Version Control
# ===========================
.git
.gitignore
.gitattributes

# ===========================
# Documentation
# ===========================
README.md
docs/
*.md

# ===========================
# Environment & Configuration
# ===========================
.env
.env.*
!.env.example

# ===========================
# Development Files
# ===========================
test.html
*.test.js
*.spec.js
cypress/
jest.config.js

# ===========================
# IDE & Editor Files
# ===========================
.vscode/
.idea/
*.sublime-*
*.swp
*.swo

# ===========================
# Operating System Files
# ===========================
.DS_Store
Thumbs.db
*.tmp
*.temp

# ===========================
# Node.js (if applicable)
# ===========================
node_modules/
npm-debug.log*
yarn-debug.log*
package-lock.json
yarn.lock

# ===========================
# Build & Distribution
# ===========================
dist/
build/
coverage/

# ===========================
# Docker Development Files
# ===========================
# Note: Keep Dockerfile and docker-compose.yml for CI/CD validation
# Dockerfile* (commented out - needed for CI/CD)
# docker-compose*.yml (commented out - needed for CI/CD)
# .dockerignore (commented out - needed for CI/CD validation)

# ===========================
# CI/CD
# ===========================
Jenkinsfile
.jenkins/
.github/
.gitlab-ci.yml
jenkins/

# ===========================
# Logs
# ===========================
logs/
*.log

# ===========================
# Temporary Files
# ===========================
tmp/
temp/
.tmp/

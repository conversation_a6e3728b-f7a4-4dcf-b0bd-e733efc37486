# Vietnam Administrative Restructuring 2025 - Dockerfile
# Multi-stage build for optimized production deployment

# ===========================
# Stage 1: Build Environment
# ===========================
FROM node:20-alpine3.19 AS builder

# Update Alpine packages to fix vulnerabilities
RUN apk update && apk upgrade --no-cache

# Optionally, switch to the latest Node.js LTS Alpine image for more recent security patches
# FROM node:20-alpine AS builder

# Set working directory
WORKDIR /app

# Copy package files (if they exist in the future)
# COPY package*.json ./
# RUN npm ci --only=production

# Copy source files
COPY . .

# Remove development files and create optimized build
RUN rm -f test.html && \
    rm -rf .git && \
    rm -f .env.example && \
    rm -f README.md

# ===========================
# Stage 2: Production Runtime
# ===========================
FROM nginx:1.27-alpine AS production

# Install envsubst for environment variable substitution
RUN apk add --no-cache gettext

# Create app directory
WORKDIR /usr/share/nginx/html

# Copy application files from builder stage
COPY --from=builder /app .

# Create nginx configuration template
COPY <<EOF /etc/nginx/templates/default.conf.template
# Vietnam Administrative Restructuring 2025 - Nginx Configuration

server {
    listen 80;
    listen [::]:80;
    server_name localhost _;

    # Security headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    add_header Content-Security-Policy "default-src 'self' 'unsafe-inline' 'unsafe-eval' https://cdn.tailwindcss.com https://cdn.jsdelivr.net https://fonts.googleapis.com https://fonts.gstatic.com https://generativelanguage.googleapis.com; img-src 'self' data: https:; font-src 'self' https://fonts.gstatic.com;" always;

    # Root directory
    root /usr/share/nginx/html;
    index index.html;

    # Enable gzip compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/javascript
        application/xml+rss
        application/json;

    # Health check endpoint (must come before deny rules)
    location /health {
        access_log off;
        return 200 '{"status":"healthy","service":"vietnam-admin-restructuring-2025"}\n';
        add_header Content-Type application/json;
    }

    # Cache static assets
    location ~* \.(css|js|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        access_log off;
    }

    # Main application
    location / {
        try_files \$uri \$uri/ /index.html;

        # Prevent caching of HTML files
        add_header Cache-Control "no-cache, no-store, must-revalidate";
        add_header Pragma "no-cache";
        add_header Expires "0";
    }

    # Deny access to sensitive files
    location ~ /\.env$ {
        deny all;
        access_log off;
        log_not_found off;
    }

    location ~ /\.git {
        deny all;
        access_log off;
        log_not_found off;
    }

    location ~ /\.dockerignore$ {
        deny all;
        access_log off;
        log_not_found off;
    }

    location ~ /Dockerfile$ {
        deny all;
        access_log off;
        log_not_found off;
    }
}
EOF

# Create environment injection script
COPY <<'EOF' /docker-entrypoint.d/30-inject-env.sh
#!/bin/sh
# Environment variable injection script for Vietnam Administrative Restructuring 2025

set -e

# Default values
GEMINI_API_KEY=${GEMINI_API_KEY:-""}
APP_NAME=${APP_NAME:-"Vietnam Administrative Restructuring 2025"}
APP_VERSION=${APP_VERSION:-"1.0.0"}
APP_ENVIRONMENT=${APP_ENVIRONMENT:-"production"}

# Create environment configuration file
cat > /usr/share/nginx/html/config/env-config.js << EOL
// Auto-generated environment configuration
// This file is created during container startup

(function() {
    'use strict';
    
    // Set environment variables on window object
    window.GEMINI_API_KEY = '${GEMINI_API_KEY}';
    window.APP_NAME = '${APP_NAME}';
    window.APP_VERSION = '${APP_VERSION}';
    window.APP_ENVIRONMENT = '${APP_ENVIRONMENT}';
    
    // Set ENV object for compatibility
    window.ENV = {
        GEMINI_API_KEY: '${GEMINI_API_KEY}',
        APP_NAME: '${APP_NAME}',
        APP_VERSION: '${APP_VERSION}',
        APP_ENVIRONMENT: '${APP_ENVIRONMENT}'
    };
    
    console.log('Environment configuration loaded:', {
        APP_NAME: window.APP_NAME,
        APP_VERSION: window.APP_VERSION,
        APP_ENVIRONMENT: window.APP_ENVIRONMENT,
        GEMINI_API_KEY: window.GEMINI_API_KEY ? 'Set' : 'Not set'
    });
})();
EOL

echo "Environment configuration injected successfully"
EOF

# Make the script executable
RUN chmod +x /docker-entrypoint.d/30-inject-env.sh

# Update index.html to include the environment configuration
RUN sed -i '/<script src="config\/env-loader.js"><\/script>/a\    <script src="config/env-config.js"></script>' /usr/share/nginx/html/index.html

# Set proper permissions
RUN chown -R nginx:nginx /usr/share/nginx/html && \
    chmod -R 755 /usr/share/nginx/html

# Expose port 80
EXPOSE 80

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD curl -f http://localhost/health || exit 1

# Labels for metadata
LABEL maintainer="Vietnam Administrative Restructuring 2025 Team"
LABEL version="1.0.0"
LABEL description="Interactive infographic for Vietnam's administrative restructuring from 63 to 34 provincial units"
LABEL org.opencontainers.image.title="Vietnam Administrative Restructuring 2025"
LABEL org.opencontainers.image.description="Web application showing proposed administrative changes with AI analysis"
LABEL org.opencontainers.image.version="1.0.0"
LABEL org.opencontainers.image.vendor="Vietnam Administrative Restructuring Project"

# Use the default nginx entrypoint which will run our environment injection script
CMD ["nginx", "-g", "daemon off;"]

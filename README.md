# Vietnam Administrative Restructuring 2025

An interactive infographic showcasing the proposed administrative restructuring of Vietnam from 63 to 34 provincial-level units, enhanced with AI-powered analysis using Google's Gemini AI.

## 🌟 Features

- **Interactive Data Visualization**: Charts showing the impact of administrative restructuring
- **AI-Powered Analysis**: Get detailed impact analysis for each merger proposal using Gemini AI
- **Responsive Design**: Works seamlessly on desktop and mobile devices
- **Modular Architecture**: Clean, maintainable code structure
- **Vietnamese Language Support**: Fully localized interface

## 🏗️ Project Structure

```
├── index.html                 # Main HTML file
├── assets/
│   ├── css/
│   │   ├── main.css          # Base styles and layout
│   │   ├── components.css    # Component-specific styles
│   │   └── animations.css    # Animation and loading styles
│   └── js/
│       ├── app.js           # Main application controller
│       ├── api.js           # API management (Gemini AI)
│       ├── charts.js        # Chart creation and management
│       ├── modal.js         # Modal functionality
│       └── utils.js         # Utility functions
├── config/
│   ├── config.js            # Application configuration
│   └── env-loader.js        # Environment variable loader
├── docs/
│   └── (documentation files)
├── .env.example             # Environment variables template
└── README.md               # This file
```

## 🚀 Quick Start

### Option 1: Local Development

#### 1. Clone the Repository

```bash
git clone <repository-url>
cd vietnam-admin-restructuring-2025
```

#### 2. Set Up Environment Variables

Copy the environment template and configure your API key:

```bash
cp .env.example .env
```

Edit `.env` and add your Gemini AI API key:

```env
GEMINI_API_KEY=your_actual_api_key_here
```

#### 3. Get a Gemini AI API Key

1. Visit [Google AI Studio](https://makersuite.google.com/app/apikey)
2. Sign in with your Google account
3. Create a new API key
4. Copy the key to your `.env` file

#### 4. Serve the Application

Since the application loads external resources and environment variables, you need to serve it through a web server:

**Using Python:**
```bash
python -m http.server 8000
```

**Using Node.js:**
```bash
npx serve .
```

**Using PHP:**
```bash
php -S localhost:8000
```

#### 5. Open in Browser

Navigate to `http://localhost:8000` in your web browser.

### Option 2: Docker Deployment

#### Quick Start with Docker

```bash
# Clone the repository
git clone <repository-url>
cd vietnam-admin-restructuring-2025

# Set up environment variables
cp .env.example .env
# Edit .env with your API key

# Build and run with Docker Compose
docker-compose up -d

# Access the application
open http://localhost:8080
```

#### Manual Docker Build

```bash
# Build the Docker image
docker build -t vietnam-admin-restructuring:latest .

# Run the container
docker run -d \
  --name vietnam-admin-app \
  -p 8080:80 \
  -e GEMINI_API_KEY="your_api_key_here" \
  vietnam-admin-restructuring:latest

# Access the application
open http://localhost:8080
```

## 🔄 Git Workflow

This project follows modern Git conventions and best practices.

### Branch Strategy

- **`main`**: Production-ready code, protected branch
- **`develop`**: Integration branch for features
- **`feature/*`**: Feature development branches
- **`hotfix/*`**: Critical bug fixes
- **`release/*`**: Release preparation branches

### Getting Started with Git

```bash
# Initialize repository (if starting fresh)
git init
git branch -M main

# Clone existing repository
git clone <repository-url>
cd vietnam-admin-restructuring-2025

# Create a feature branch
git checkout -b feature/your-feature-name

# Make changes and commit
git add .
git commit -m "feat: add new feature description"

# Push feature branch
git push origin feature/your-feature-name

# Create pull request for review
```

### Commit Message Convention

Follow conventional commits format:

```
type(scope): description

[optional body]

[optional footer]
```

**Types:**
- `feat`: New feature
- `fix`: Bug fix
- `docs`: Documentation changes
- `style`: Code style changes
- `refactor`: Code refactoring
- `test`: Adding tests
- `chore`: Maintenance tasks

**Examples:**
```bash
git commit -m "feat(api): add Gemini AI integration"
git commit -m "fix(charts): resolve chart rendering issue"
git commit -m "docs(readme): update installation instructions"
```

## 🐳 Docker Usage

### Development with Docker

```bash
# Start development environment
docker-compose --profile development up -d

# View logs
docker-compose logs -f web-dev

# Stop development environment
docker-compose --profile development down
```

### Production Deployment

```bash
# Build production image
docker build -t vietnam-admin-restructuring:prod .

# Run production container
docker run -d \
  --name vietnam-admin-prod \
  -p 80:80 \
  -e GEMINI_API_KEY="${GEMINI_API_KEY}" \
  -e APP_ENVIRONMENT="production" \
  vietnam-admin-restructuring:prod
```

### Docker Commands Reference

```bash
# Build image
docker build -t vietnam-admin-restructuring .

# Run container with environment variables
docker run -d \
  --name vietnam-admin-app \
  -p 8080:80 \
  -e GEMINI_API_KEY="your_key" \
  -e APP_NAME="Custom App Name" \
  vietnam-admin-restructuring

# View container logs
docker logs vietnam-admin-app

# Execute commands in container
docker exec -it vietnam-admin-app sh

# Stop and remove container
docker stop vietnam-admin-app
docker rm vietnam-admin-app

# Remove image
docker rmi vietnam-admin-restructuring
```

### Docker Compose Commands

```bash
# Start all services
docker-compose up -d

# Start specific service
docker-compose up -d web

# View logs
docker-compose logs -f

# Stop all services
docker-compose down

# Rebuild and start
docker-compose up -d --build

# Remove all containers and volumes
docker-compose down -v
```

## 🔧 Configuration

### Environment Variables

The application supports the following environment variables:

| Variable | Description | Required |
|----------|-------------|----------|
| `GEMINI_API_KEY` | Your Gemini AI API key | Yes (for AI features) |
| `APP_NAME` | Application name | No |
| `APP_VERSION` | Application version | No |
| `APP_ENVIRONMENT` | Environment (development/production) | No |

### Setting Environment Variables

#### Method 1: .env File (Recommended for Development)
Create a `.env` file in the project root with your variables.

#### Method 2: JavaScript (For Development/Testing)
Add variables directly in your HTML before loading the application:

```html
<script>
    window.GEMINI_API_KEY = 'your_api_key_here';
</script>
```

#### Method 3: Build Process (For Production)
Use your build tool to inject environment variables during the build process.

## 📊 Features Overview

### Data Visualization
- **Proportion Chart**: Shows the ratio of provinces to be merged vs. those remaining unchanged
- **Regional Impact Chart**: Compares the number of administrative units by region before and after restructuring

### AI Analysis
- Click "✨ Phân tích Tác động" on any merger proposal
- Get detailed analysis of potential economic and social impacts
- Analysis includes both advantages and challenges of each merger

### Responsive Design
- Mobile-first approach
- Optimized for various screen sizes
- Touch-friendly interface

## 🛠️ Development

### Code Structure

The application follows a modular architecture:

- **App.js**: Main application controller that coordinates all modules
- **ChartsManager**: Handles Chart.js integration and data visualization
- **ModalManager**: Manages modal display and user interactions
- **ApiManager**: Handles all API calls to Gemini AI
- **Utils**: Common utility functions used throughout the application

### Adding New Features

1. Create new modules in `assets/js/`
2. Update `index.html` to include the new script
3. Initialize the module in `app.js`
4. Update configuration in `config/config.js` if needed

### Styling

The application uses:
- **Tailwind CSS**: For utility-first styling
- **Custom CSS**: For component-specific styles
- **CSS Modules**: Organized into separate files by purpose

## 🚀 CI/CD with Jenkins

This project includes a comprehensive Jenkins pipeline for automated building, testing, and deployment.

### Jenkins Setup

#### Prerequisites
- Jenkins 2.400+ with required plugins
- Docker support on Jenkins agents
- Access to Docker registry

#### Required Jenkins Plugins
```
- Pipeline
- Docker Pipeline
- Git
- Credentials Binding
- Email Extension (optional)
```

#### Quick Setup

1. **Install Plugins**: Install required plugins in Jenkins
2. **Configure Credentials**: Add Docker registry and deployment credentials
3. **Create Pipeline Job**: Create new pipeline job pointing to this repository
4. **Configure Webhooks**: Set up Git webhooks for automatic builds

For detailed setup instructions, see [jenkins/README.md](jenkins/README.md).

### Pipeline Stages

The Jenkins pipeline includes the following stages:

1. **Checkout & Validation**: Code checkout and project structure validation
2. **Security Scan**: Source code and dependency security scanning
3. **Build**: Docker image building and tagging
4. **Test**: Functional and performance testing
5. **Security Scan**: Docker image vulnerability scanning
6. **Push**: Push to Docker registry (main/develop branches only)
7. **Deploy**: Automated deployment to staging/production

### Environment-Specific Deployments

#### Staging Deployment
- **Trigger**: Push to `develop` branch
- **Automatic**: Yes
- **Environment**: Staging with test API keys

#### Production Deployment
- **Trigger**: Push to `main` branch
- **Approval**: Manual approval required
- **Environment**: Production with live API keys

### Pipeline Configuration

The pipeline uses environment variables and Jenkins credentials:

```groovy
environment {
    APP_NAME = 'vietnam-admin-restructuring-2025'
    DOCKER_REGISTRY = credentials('docker-registry-url')
    STAGING_URL = credentials('staging-url')
    PRODUCTION_URL = credentials('production-url')
}
```

### Security Best Practices

- ✅ No hardcoded secrets in pipeline
- ✅ Environment-specific credentials
- ✅ Docker image security scanning
- ✅ Manual approval for production
- ✅ Audit trail for all deployments

### Monitoring and Notifications

The pipeline includes:
- Build status notifications
- Security scan reports
- Deployment confirmations
- Performance metrics

## 🔒 Security Considerations

### API Key Security

⚠️ **Important**: Never expose API keys in client-side code in production!

For production deployments:
1. Use a backend proxy to handle API calls
2. Implement proper authentication and authorization
3. Use environment-specific build processes
4. Consider using secure token exchange mechanisms

### Containerized Deployment Security

For containerized deployments:

#### Development/Testing
```bash
# Use test API key
docker run -e GEMINI_API_KEY="test_key_here" vietnam-admin-restructuring
```

#### Production
```bash
# Use production API key from secure source
docker run -e GEMINI_API_KEY="${PROD_API_KEY}" vietnam-admin-restructuring
```

#### Kubernetes Deployment
```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: vietnam-admin-app
spec:
  template:
    spec:
      containers:
      - name: app
        image: vietnam-admin-restructuring:latest
        env:
        - name: GEMINI_API_KEY
          valueFrom:
            secretKeyRef:
              name: api-keys
              key: gemini-api-key
```

### Current Implementation

The current implementation supports both development and production scenarios:

#### Development Mode
- API key loaded client-side for quick testing
- Suitable for local development and demos
- Environment variables injected at runtime

#### Production Mode
- API key injected via environment variables
- Container-based deployment with secure secrets
- No client-side exposure of sensitive data

## 🌐 Browser Support

- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+

## 📝 License

This project is for educational and demonstration purposes.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📞 Support

For questions or issues:
1. Check the documentation in the `docs/` folder
2. Review the browser console for error messages
3. Ensure your API key is properly configured
4. Verify you're serving the application through a web server

## 🚀 Deployment Guide

### Local Deployment

```bash
# Development server
python -m http.server 8000

# Access at http://localhost:8000
```

### Docker Deployment

#### Single Container
```bash
# Build and run
docker build -t vietnam-admin-app .
docker run -d -p 8080:80 \
  -e GEMINI_API_KEY="your_key" \
  vietnam-admin-app
```

#### Docker Compose
```bash
# Production deployment
docker-compose up -d

# Development with live reload
docker-compose --profile development up -d
```

### Cloud Deployment

#### AWS ECS
```bash
# Build and push to ECR
aws ecr get-login-password --region us-east-1 | docker login --username AWS --password-stdin <account>.dkr.ecr.us-east-1.amazonaws.com
docker build -t vietnam-admin-app .
docker tag vietnam-admin-app:latest <account>.dkr.ecr.us-east-1.amazonaws.com/vietnam-admin-app:latest
docker push <account>.dkr.ecr.us-east-1.amazonaws.com/vietnam-admin-app:latest
```

#### Google Cloud Run
```bash
# Build and deploy
gcloud builds submit --tag gcr.io/PROJECT-ID/vietnam-admin-app
gcloud run deploy --image gcr.io/PROJECT-ID/vietnam-admin-app --platform managed
```

#### Azure Container Instances
```bash
# Create resource group and deploy
az group create --name vietnam-admin-rg --location eastus
az container create \
  --resource-group vietnam-admin-rg \
  --name vietnam-admin-app \
  --image vietnam-admin-app:latest \
  --ports 80 \
  --environment-variables GEMINI_API_KEY="your_key"
```

### Kubernetes Deployment

```yaml
# deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: vietnam-admin-app
spec:
  replicas: 3
  selector:
    matchLabels:
      app: vietnam-admin-app
  template:
    metadata:
      labels:
        app: vietnam-admin-app
    spec:
      containers:
      - name: app
        image: vietnam-admin-app:latest
        ports:
        - containerPort: 80
        env:
        - name: GEMINI_API_KEY
          valueFrom:
            secretKeyRef:
              name: api-secrets
              key: gemini-api-key
---
apiVersion: v1
kind: Service
metadata:
  name: vietnam-admin-service
spec:
  selector:
    app: vietnam-admin-app
  ports:
  - port: 80
    targetPort: 80
  type: LoadBalancer
```

Deploy with:
```bash
kubectl apply -f deployment.yaml
```

## 🔄 Version History

- **v1.1.0**: Production-ready deployment
  - Added Docker containerization
  - Implemented CI/CD pipeline with Jenkins
  - Enhanced security with environment variable injection
  - Added comprehensive deployment options
  - Improved documentation and setup guides

- **v1.0.0**: Initial modular refactoring
  - Separated concerns into modules
  - Added environment configuration
  - Improved code organization
  - Enhanced documentation

<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Infographic về phương án tái cấu trúc hành chính Việt Nam 2025, từ 63 xuống 34 đơn vị cấp tỉnh với phân tích AI">
    <meta name="keywords" content="Việt Nam, hành chính, tái cấu trúc, 2025, sáp nhập, tỉnh thành">
    <meta name="author" content="Vietnam Administrative Restructuring 2025">

    <title>Infographic: Tái cấu trúc Hành chính Việt Nam 2025 (với Gemini AI)</title>

    <!-- External Dependencies -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Be+Vietnam+Pro:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- Application Styles -->
    <link rel="stylesheet" href="assets/css/main.css">
    <link rel="stylesheet" href="assets/css/components.css">
    <link rel="stylesheet" href="assets/css/animations.css">
</head>
<body class="text-gray-800">
    <!-- Application Header -->
    <header class="bg-white shadow-md p-6 text-center">
        <h1 class="text-3xl md:text-4xl font-bold text-[#00487C]">Tái cấu trúc Bản đồ Hành chính Việt Nam 2025</h1>
        <p class="text-lg md:text-xl text-gray-600 mt-2">Phương án sắp xếp từ 63 xuống còn 34 đơn vị hành chính cấp Tỉnh</p>
    </header>

    <!-- Main Content -->
    <main class="container mx-auto p-4 md:p-8">
        <!-- Overview Section -->
        <section id="overview" class="mb-12">
            <h2 class="text-2xl font-bold text-center mb-8 text-[#00487C]">Tổng Quan Cuộc Sắp Xếp</h2>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-8 text-center">
                <div class="bg-white p-6 rounded-lg shadow-lg">
                    <p class="text-6xl font-bold text-[#027BCE]">63</p>
                    <p class="text-lg font-semibold mt-2">Đơn vị hành chính cũ</p>
                </div>
                <div class="bg-white p-6 rounded-lg shadow-lg">
                    <p class="text-6xl font-bold text-[#0496FF]">&#8594;</p>
                    <p class="text-lg font-semibold mt-2">Sắp xếp & Sáp nhập</p>
                </div>
                <div class="bg-white p-6 rounded-lg shadow-lg">
                    <p class="text-6xl font-bold text-[#00487C]">34</p>
                    <p class="text-lg font-semibold mt-2">Đơn vị hành chính mới (Dự kiến)</p>
                </div>
            </div>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-8 mt-10">
                <div class="bg-white p-6 rounded-lg shadow-lg">
                    <h3 class="text-xl font-bold text-center mb-4">Phân Bố Tỷ Lệ Sắp Xếp</h3>
                     <p class="text-sm text-gray-600 text-center mb-4">Biểu đồ thể hiện tỷ lệ các tỉnh thành thuộc diện sáp nhập so với các tỉnh thành được giữ nguyên theo phương án dự kiến.</p>
                    <div class="chart-container h-64 md:h-72">
                        <canvas id="proportionChart"></canvas>
                    </div>
                </div>
                 <div class="bg-white p-6 rounded-lg shadow-lg">
                    <h3 class="text-xl font-bold text-center mb-4">Số Lượng ĐVHC Theo Vùng</h3>
                    <p class="text-sm text-gray-600 text-center mb-4">So sánh số lượng đơn vị hành chính cấp tỉnh tại các vùng kinh tế trọng điểm trước và sau khi thực hiện sáp nhập.</p>
                    <div class="chart-container h-64 md:h-72">
                        <canvas id="regionalImpactChart"></canvas>
                    </div>
                </div>
            </div>
        </section>

        <!-- Merger Details Section -->
        <section id="mergers">
            <h2 class="text-2xl font-bold text-center mb-8 text-[#00487C]">Chi Tiết Phương Án Sáp Nhập Các Tỉnh</h2>
            <p class="text-center text-gray-600 max-w-3xl mx-auto mb-10">Dưới đây là danh sách chi tiết các phương án hợp nhất các tỉnh thành, được nhóm theo khu vực địa lý. Nhấn vào nút "✨ Phân tích Tác động" để xem đánh giá do AI tạo ra.</p>
            
            <div class="space-y-8">
                <div>
                    <h3 class="text-xl font-semibold mb-4 text-center bg-[#4BB3FD] text-white py-2 rounded-lg">Khu Vực Phía Bắc</h3>
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        <div class="bg-white p-4 rounded-lg shadow-md flow-card"><div class="flow-card-content"><div class="flow-item">Hà Giang</div><div class="flow-arrow">+</div><div class="flow-item">Tuyên Quang</div><div class="flow-arrow">→</div><div class="flow-result">T. Tuyên Quang</div></div><button class="gemini-btn analyze-btn" data-provinces="Hà Giang và Tuyên Quang">✨ Phân tích Tác động</button></div>
                        <div class="bg-white p-4 rounded-lg shadow-md flow-card"><div class="flow-card-content"><div class="flow-item">Cao Bằng</div><div class="flow-arrow">+</div><div class="flow-item">Bắc Kạn</div><div class="flow-arrow">+</div><div class="flow-item">Thái Nguyên</div><div class="flow-arrow">→</div><div class="flow-result">T. Thái Nguyên</div></div><button class="gemini-btn analyze-btn" data-provinces="Cao Bằng, Bắc Kạn và Thái Nguyên">✨ Phân tích Tác động</button></div>
                        <div class="bg-white p-4 rounded-lg shadow-md flow-card"><div class="flow-card-content"><div class="flow-item">Lào Cai</div><div class="flow-arrow">+</div><div class="flow-item">Yên Bái</div><div class="flow-arrow">→</div><div class="flow-result">T. Lào Cai</div></div><button class="gemini-btn analyze-btn" data-provinces="Lào Cai và Yên Bái">✨ Phân tích Tác động</button></div>
                        <div class="bg-white p-4 rounded-lg shadow-md flow-card"><div class="flow-card-content"><div class="flow-item">Hòa Bình</div><div class="flow-arrow">+</div><div class="flow-item">Vĩnh Phúc</div><div class="flow-arrow">+</div><div class="flow-item">Phú Thọ</div><div class="flow-arrow">→</div><div class="flow-result">T. Phú Thọ</div></div><button class="gemini-btn analyze-btn" data-provinces="Hòa Bình, Vĩnh Phúc và Phú Thọ">✨ Phân tích Tác động</button></div>
                        <div class="bg-white p-4 rounded-lg shadow-md flow-card"><div class="flow-card-content"><div class="flow-item">Bắc Ninh</div><div class="flow-arrow">+</div><div class="flow-item">Bắc Giang</div><div class="flow-arrow">→</div><div class="flow-result">T. Bắc Ninh</div></div><button class="gemini-btn analyze-btn" data-provinces="Bắc Ninh và Bắc Giang">✨ Phân tích Tác động</button></div>
                        <div class="bg-white p-4 rounded-lg shadow-md flow-card"><div class="flow-card-content"><div class="flow-item">Hà Nam</div><div class="flow-arrow">+</div><div class="flow-item">Nam Định</div><div class="flow-arrow">+</div><div class="flow-item">Ninh Bình</div><div class="flow-arrow">→</div><div class="flow-result">T. Ninh Bình</div></div><button class="gemini-btn analyze-btn" data-provinces="Hà Nam, Nam Định và Ninh Bình">✨ Phân tích Tác động</button></div>
                        <div class="bg-white p-4 rounded-lg shadow-md flow-card"><div class="flow-card-content"><div class="flow-item">Hải Dương</div><div class="flow-arrow">+</div><div class="flow-item">Hải Phòng</div><div class="flow-arrow">→</div><div class="flow-result">TP. Hải Phòng</div></div><button class="gemini-btn analyze-btn" data-provinces="Hải Dương và Hải Phòng">✨ Phân tích Tác động</button></div>
                        <div class="bg-white p-4 rounded-lg shadow-md flow-card"><div class="flow-card-content"><div class="flow-item">Hưng Yên</div><div class="flow-arrow">+</div><div class="flow-item">Thái Bình</div><div class="flow-arrow">→</div><div class="flow-result">T. Hưng Yên</div></div><button class="gemini-btn analyze-btn" data-provinces="Hưng Yên và Thái Bình">✨ Phân tích Tác động</button></div>
                    </div>
                </div>

                <div>
                    <h3 class="text-xl font-semibold mb-4 text-center bg-[#4BB3FD] text-white py-2 rounded-lg">Khu Vực Miền Trung & Tây Nguyên</h3>
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        <div class="bg-white p-4 rounded-lg shadow-md flow-card"><div class="flow-card-content"><div class="flow-item">Quảng Bình</div><div class="flow-arrow">+</div><div class="flow-item">Quảng Trị</div><div class="flow-arrow">→</div><div class="flow-result">T. Quảng Trị</div></div><button class="gemini-btn analyze-btn" data-provinces="Quảng Bình và Quảng Trị">✨ Phân tích Tác động</button></div>
                        <div class="bg-white p-4 rounded-lg shadow-md flow-card"><div class="flow-card-content"><div class="flow-item">Đà Nẵng</div><div class="flow-arrow">+</div><div class="flow-item">Quảng Nam</div><div class="flow-arrow">→</div><div class="flow-result">TP. Đà Nẵng</div></div><button class="gemini-btn analyze-btn" data-provinces="Đà Nẵng và Quảng Nam">✨ Phân tích Tác động</button></div>
                        <div class="bg-white p-4 rounded-lg shadow-md flow-card"><div class="flow-card-content"><div class="flow-item">Quảng Ngãi</div><div class="flow-arrow">+</div><div class="flow-item">Kon Tum</div><div class="flow-arrow">→</div><div class="flow-result">T. Quảng Ngãi</div></div><button class="gemini-btn analyze-btn" data-provinces="Quảng Ngãi và Kon Tum">✨ Phân tích Tác động</button></div>
                        <div class="bg-white p-4 rounded-lg shadow-md flow-card"><div class="flow-card-content"><div class="flow-item">Bình Định</div><div class="flow-arrow">+</div><div class="flow-item">Gia Lai</div><div class="flow-arrow">→</div><div class="flow-result">T. Gia Lai</div></div><button class="gemini-btn analyze-btn" data-provinces="Bình Định và Gia Lai">✨ Phân tích Tác động</button></div>
                        <div class="bg-white p-4 rounded-lg shadow-md flow-card"><div class="flow-card-content"><div class="flow-item">Phú Yên</div><div class="flow-arrow">+</div><div class="flow-item">Đắk Lắk</div><div class="flow-arrow">→</div><div class="flow-result">T. Đắk Lắk</div></div><button class="gemini-btn analyze-btn" data-provinces="Phú Yên và Đắk Lắk">✨ Phân tích Tác động</button></div>
                        <div class="bg-white p-4 rounded-lg shadow-md flow-card"><div class="flow-card-content"><div class="flow-item">Khánh Hòa</div><div class="flow-arrow">+</div><div class="flow-item">Ninh Thuận</div><div class="flow-arrow">→</div><div class="flow-result">T. Khánh Hòa</div></div><button class="gemini-btn analyze-btn" data-provinces="Khánh Hòa và Ninh Thuận">✨ Phân tích Tác động</button></div>
                        <div class="bg-white p-4 rounded-lg shadow-md flow-card"><div class="flow-card-content"><div class="flow-item">Bình Thuận</div><div class="flow-arrow">+</div><div class="flow-item">Đắk Nông</div><div class="flow-arrow">+</div><div class="flow-item">Lâm Đồng</div><div class="flow-arrow">→</div><div class="flow-result">T. Lâm Đồng</div></div><button class="gemini-btn analyze-btn" data-provinces="Bình Thuận, Đắk Nông và Lâm Đồng">✨ Phân tích Tác động</button></div>
                    </div>
                </div>

                <div>
                    <h3 class="text-xl font-semibold mb-4 text-center bg-[#4BB3FD] text-white py-2 rounded-lg">Khu Vực Phía Nam</h3>
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                         <div class="bg-white p-4 rounded-lg shadow-md flow-card"><div class="flow-card-content"><div class="flow-item">Bình Phước</div><div class="flow-arrow">+</div><div class="flow-item">Đồng Nai</div><div class="flow-arrow">→</div><div class="flow-result">T. Đồng Nai</div></div><button class="gemini-btn analyze-btn" data-provinces="Bình Phước và Đồng Nai">✨ Phân tích Tác động</button></div>
                        <div class="bg-white p-4 rounded-lg shadow-md flow-card"><div class="flow-card-content"><div class="flow-item">Tây Ninh</div><div class="flow-arrow">+</div><div class="flow-item">Long An</div><div class="flow-arrow">→</div><div class="flow-result">T. Tây Ninh</div></div><button class="gemini-btn analyze-btn" data-provinces="Tây Ninh và Long An">✨ Phân tích Tác động</button></div>
                        <div class="bg-white p-4 rounded-lg shadow-md flow-card"><div class="flow-card-content"><div class="flow-item">Bà Rịa-Vũng Tàu</div><div class="flow-arrow">+</div><div class="flow-item">Bình Dương</div><div class="flow-arrow">+</div><div class="flow-item">TP.HCM</div><div class="flow-arrow">→</div><div class="flow-result">TP. Hồ Chí Minh</div></div><button class="gemini-btn analyze-btn" data-provinces="Bà Rịa-Vũng Tàu, Bình Dương và TP.HCM">✨ Phân tích Tác động</button></div>
                        <div class="bg-white p-4 rounded-lg shadow-md flow-card"><div class="flow-card-content"><div class="flow-item">Tiền Giang</div><div class="flow-arrow">+</div><div class="flow-item">Đồng Tháp</div><div class="flow-arrow">→</div><div class="flow-result">T. Đồng Tháp</div></div><button class="gemini-btn analyze-btn" data-provinces="Tiền Giang và Đồng Tháp">✨ Phân tích Tác động</button></div>
                        <div class="bg-white p-4 rounded-lg shadow-md flow-card"><div class="flow-card-content"><div class="flow-item">Vĩnh Long</div><div class="flow-arrow">+</div><div class="flow-item">Bến Tre</div><div class="flow-arrow">+</div><div class="flow-item">Trà Vinh</div><div class="flow-arrow">→</div><div class="flow-result">T. Vĩnh Long</div></div><button class="gemini-btn analyze-btn" data-provinces="Vĩnh Long, Bến Tre và Trà Vinh">✨ Phân tích Tác động</button></div>
                        <div class="bg-white p-4 rounded-lg shadow-md flow-card"><div class="flow-card-content"><div class="flow-item">Cần Thơ</div><div class="flow-arrow">+</div><div class="flow-item">Sóc Trăng</div><div class="flow-arrow">+</div><div class="flow-item">Hậu Giang</div><div class="flow-arrow">→</div><div class="flow-result">TP. Cần Thơ</div></div><button class="gemini-btn analyze-btn" data-provinces="Cần Thơ, Sóc Trăng và Hậu Giang">✨ Phân tích Tác động</button></div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Unchanged Provinces Section -->
        <section id="unchanged" class="mt-12">
            <h2 class="text-2xl font-bold text-center mb-8 text-[#00487C]">Các Tỉnh Thành Dự Kiến Giữ Nguyên</h2>
            <p class="text-center text-gray-600 max-w-3xl mx-auto mb-10">13 đơn vị hành chính cấp tỉnh dưới đây dự kiến sẽ không thuộc diện sắp xếp trong đợt này. Trong đó, Thừa Thiên Huế được định hướng trở thành Thành phố trực thuộc Trung ương.</p>
            <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 text-center">
                <div class="bg-white p-4 rounded-lg shadow-md font-semibold text-[#00487C]">Hà Nội</div>
                <div class="bg-white p-4 rounded-lg shadow-md font-semibold text-[#00487C]">Lai Châu</div>
                <div class="bg-white p-4 rounded-lg shadow-md font-semibold text-[#00487C]">Điện Biên</div>
                <div class="bg-white p-4 rounded-lg shadow-md font-semibold text-[#00487C]">Sơn La</div>
                <div class="bg-white p-4 rounded-lg shadow-md font-semibold text-[#00487C]">Lạng Sơn</div>
                <div class="bg-white p-4 rounded-lg shadow-md font-semibold text-[#00487C]">Quảng Ninh</div>
                <div class="bg-white p-4 rounded-lg shadow-md font-semibold text-[#00487C]">Thanh Hóa</div>
                <div class="bg-white p-4 rounded-lg shadow-md font-semibold text-[#00487C]">Nghệ An</div>
                <div class="bg-white p-4 rounded-lg shadow-md font-semibold text-[#00487C]">Hà Tĩnh</div>
                <div class="bg-white p-4 rounded-lg shadow-md font-semibold text-[#027BCE]">Thừa Thiên Huế*</div>
                <div class="bg-white p-4 rounded-lg shadow-md font-semibold text-[#00487C]">An Giang</div>
                <div class="bg-white p-4 rounded-lg shadow-md font-semibold text-[#00487C]">Kiên Giang</div>
                <div class="bg-white p-4 rounded-lg shadow-md font-semibold text-[#00487C]">Cà Mau</div>
            </div>
        </section>
    </main>

    <!-- AI Analysis Modal -->
    <div id="geminiModal" class="fixed inset-0 bg-black bg-opacity-50 z-50 hidden items-center justify-center p-4">
        <div class="bg-white rounded-lg shadow-2xl w-full max-w-2xl max-h-[90vh] flex flex-col">
            <div class="flex justify-between items-center p-4 border-b">
                <h3 id="modalTitle" class="text-xl font-bold text-[#00487C]">✨ Phân tích Tác động Kinh tế - Xã hội</h3>
                <button id="closeModalBtn" class="text-gray-500 hover:text-gray-800 text-2xl">&times;</button>
            </div>
            <div id="modalContent" class="p-6 overflow-y-auto">
                <div class="flex justify-center items-center h-48">
                    <div class="loader"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- AI Disclaimer Section -->
    <section class="ai-disclaimer mt-12 py-8 px-4">
        <div class="container mx-auto max-w-4xl">
            <div class="flex items-start space-x-4">
                <div class="flex-shrink-0">
                    <span class="text-3xl ai-disclaimer-icon">⚠️</span>
                </div>
                <div class="flex-1">
                    <h3 class="text-xl font-bold text-amber-800 mb-4">Tuyên bố Miễn trừ Trách nhiệm về Phân tích AI</h3>
                    <div class="text-gray-700 space-y-3 text-sm md:text-base leading-relaxed">
                        <p class="font-semibold text-amber-700">
                            <span class="inline-block w-2 h-2 bg-amber-500 rounded-full mr-2 ai-disclaimer-bullet"></span>
                            Tất cả các phân tích và đánh giá trong ứng dụng này được tạo ra bởi Trí tuệ Nhân tạo (Gemini AI).
                        </p>
                        <p>
                            <span class="inline-block w-2 h-2 bg-amber-500 rounded-full mr-2 ai-disclaimer-bullet"></span>
                            Nội dung do AI tạo ra chỉ mang tính chất <strong>tham khảo và giáo dục</strong>, không thể thay thế cho việc nghiên cứu chuyên sâu hoặc tư vấn chuyên môn.
                        </p>
                        <p>
                            <span class="inline-block w-2 h-2 bg-amber-500 rounded-full mr-2 ai-disclaimer-bullet"></span>
                            Người dùng <strong>không nên dựa hoàn toàn</strong> vào phân tích AI để đưa ra các quyết định chính thức hoặc quan trọng liên quan đến chính sách hành chính.
                        </p>
                        <p>
                            <span class="inline-block w-2 h-2 bg-amber-500 rounded-full mr-2 ai-disclaimer-bullet"></span>
                            Thông tin có thể chứa <strong>sai sót hoặc thiếu chính xác</strong> và cần được kiểm chứng với các nguồn chính thức từ cơ quan có thẩm quyền.
                        </p>
                        <p>
                            <span class="inline-block w-2 h-2 bg-amber-500 rounded-full mr-2 ai-disclaimer-bullet"></span>
                            Nhóm phát triển <strong>không chịu trách nhiệm</strong> đối với bất kỳ quyết định nào được đưa ra dựa trên nội dung phân tích do AI tạo ra.
                        </p>
                        <p class="text-xs text-gray-600 mt-4 pt-3 border-t border-amber-200">
                            Để có thông tin chính xác và cập nhật nhất, vui lòng tham khảo các văn bản chính thức từ Quốc hội, Chính phủ và các bộ, ngành có liên quan.
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Application Footer -->
    <footer class="text-center p-6 mt-8 bg-gray-800 text-white">
        <p>Thông tin được tổng hợp dựa trên các dự thảo và nghị quyết đã công bố.</p>
        <p class="text-sm text-gray-400 mt-2">Quyết định cuối cùng thuộc về các cơ quan có thẩm quyền.</p>
        <p class="text-xs text-gray-500 mt-3">
            Ứng dụng sử dụng Gemini AI để phân tích • Phiên bản 1.0.0 • 2025
        </p>
    </footer>
    
    <!-- Environment and Configuration -->
    <script src="config/env-loader.js"></script>
    <script src="config/config.js"></script>

    <!-- Application modules -->
    <script src="assets/js/utils.js"></script>
    <script src="assets/js/api.js"></script>
    <script src="assets/js/charts.js"></script>
    <script src="assets/js/modal.js"></script>
    <script src="assets/js/app.js"></script>
</body>
</html>

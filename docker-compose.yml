# Vietnam Administrative Restructuring 2025 - Docker Compose Configuration
# This file defines the services for running the application in containers

version: '3.8'

services:
  # Main web application service
  web:
    build:
      context: .
      dockerfile: Dockerfile
      target: production
    container_name: vietnam-admin-restructuring-web
    ports:
      - "8080:80"
    environment:
      # Environment variables for the application
      # These can be overridden by creating a .env file
      - GEMINI_API_KEY=${GEMINI_API_KEY:-}
      - APP_NAME=${APP_NAME:-Vietnam Administrative Restructuring 2025}
      - APP_VERSION=${APP_VERSION:-1.0.0}
      - APP_ENVIRONMENT=${APP_ENVIRONMENT:-production}
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.vietnam-admin.rule=Host(`localhost`)"
      - "traefik.http.services.vietnam-admin.loadbalancer.server.port=80"
    networks:
      - vietnam-admin-network

  # Optional: Development service with live reload
  web-dev:
    image: nginx:1.25-alpine
    container_name: vietnam-admin-restructuring-dev
    ports:
      - "8081:80"
    volumes:
      # Mount source code for development
      - ./:/usr/share/nginx/html:ro
      - ./docker/nginx-dev.conf:/etc/nginx/conf.d/default.conf:ro
    environment:
      - APP_ENVIRONMENT=development
    restart: unless-stopped
    profiles:
      - development
    networks:
      - vietnam-admin-network

networks:
  vietnam-admin-network:
    driver: bridge
    name: vietnam-admin-network

# Volumes for persistent data (if needed in the future)
volumes:
  app-data:
    driver: local
    name: vietnam-admin-data

# Jenkins CI/CD Setup Guide

This guide explains how to set up the Jenkins pipeline for the Vietnam Administrative Restructuring 2025 project.

## Prerequisites

### Jenkins Requirements
- Jenkins 2.400+ with Pipeline plugin
- Docker plugin for Jenkins
- Git plugin
- Credentials plugin
- Email Extension plugin (optional, for notifications)

### System Requirements
- Docker installed on Jenkins agents
- Access to a Docker registry
- Network access to deployment targets

## Jenkins Configuration

### 1. Install Required Plugins

Install the following plugins in <PERSON>:

```
- Pipeline
- Docker Pipeline
- Git
- Credentials Binding
- Email Extension (optional)
- Blue Ocean (optional, for better UI)
```

### 2. Configure Credentials

Add the following credentials in Jenkins (Manage Jenkins > Manage Credentials):

#### Docker Registry Credentials
- **ID**: `docker-registry-credentials`
- **Type**: Username with password
- **Username**: Your Docker registry username
- **Password**: Your Docker registry password/token

#### Docker Registry URL
- **ID**: `docker-registry-url`
- **Type**: Secret text
- **Secret**: Your Docker registry URL (e.g., `registry.example.com`)

#### Environment URLs
- **ID**: `staging-url`
- **Type**: Secret text
- **Secret**: Your staging environment URL

- **ID**: `production-url`
- **Type**: Secret text
- **Secret**: Your production environment URL

#### API Keys (for deployment)
- **ID**: `gemini-api-key-staging`
- **Type**: Secret text
- **Secret**: Gemini API key for staging

- **ID**: `gemini-api-key-production`
- **Type**: Secret text
- **Secret**: Gemini API key for production

### 3. Create Pipeline Job

1. Go to Jenkins dashboard
2. Click "New Item"
3. Enter job name: `vietnam-admin-restructuring-2025`
4. Select "Pipeline"
5. Click "OK"

### 4. Configure Pipeline

In the pipeline configuration:

#### General Settings
- ✅ GitHub project (if using GitHub)
- ✅ Discard old builds (keep last 10)

#### Build Triggers
- ✅ Poll SCM: `H/5 * * * *` (every 5 minutes)
- ✅ Build periodically: `0 2 * * *` (daily at 2 AM for security scans)

#### Pipeline Definition
- **Definition**: Pipeline script from SCM
- **SCM**: Git
- **Repository URL**: Your repository URL
- **Credentials**: Your Git credentials
- **Branch**: `*/main` (or your default branch)
- **Script Path**: `Jenkinsfile`

## Pipeline Stages Explained

### 1. Checkout & Validation
- Checks out source code
- Validates project structure
- Ensures required files exist

### 2. Security Scan - Source Code
- **Secret Detection**: Scans for hardcoded secrets
- **Dependency Check**: Analyzes external dependencies

### 3. Build Docker Image
- Builds Docker image using Dockerfile
- Tags image with build number
- Tags as 'latest' for main branch

### 4. Security Scan - Docker Image
- Uses Trivy to scan for vulnerabilities
- Generates security report
- Warns about critical vulnerabilities

### 5. Test
- **Functional Tests**: Tests application functionality
- **Performance Tests**: Basic performance validation

### 6. Push to Registry
- Pushes Docker image to registry
- Only for main/develop branches or tags

### 7. Deploy to Staging
- Deploys to staging environment
- Only for develop branch

### 8. Deploy to Production
- Requires manual approval
- Deploys to production environment
- Only for main branch

## Environment Variables

The pipeline uses these environment variables:

| Variable | Description | Source |
|----------|-------------|---------|
| `APP_NAME` | Application name | Pipeline |
| `APP_VERSION` | Build number | Jenkins |
| `DOCKER_REGISTRY` | Docker registry URL | Credentials |
| `DOCKER_REPO` | Docker repository path | Calculated |
| `DOCKER_TAG` | Docker image tag | Build number |
| `STAGING_URL` | Staging environment URL | Credentials |
| `PRODUCTION_URL` | Production environment URL | Credentials |

## Security Best Practices

### 1. Credentials Management
- ✅ All sensitive data stored in Jenkins credentials
- ✅ No hardcoded secrets in pipeline
- ✅ Environment-specific API keys

### 2. Image Security
- ✅ Trivy scanning for vulnerabilities
- ✅ Base image security (nginx:alpine)
- ✅ Minimal attack surface

### 3. Access Control
- ✅ Manual approval for production deployments
- ✅ Branch-based deployment restrictions
- ✅ Audit trail for deployments

## Customization

### Adding New Environments

To add a new environment (e.g., QA):

1. Add credentials for the environment
2. Add a new stage in the Jenkinsfile:

```groovy
stage('Deploy to QA') {
    when {
        branch 'qa'
    }
    steps {
        script {
            echo "🚀 Deploying to QA environment..."
            // Add QA deployment commands
        }
    }
}
```

### Custom Deployment Commands

Replace the deployment commands in the pipeline with your specific deployment method:

```groovy
// Example: Kubernetes deployment
sh '''
    kubectl set image deployment/vietnam-admin-app \
        app=${DOCKER_REPO}:${DOCKER_TAG} \
        --namespace=production
'''

// Example: Docker Compose deployment
sh '''
    docker-compose -f docker-compose.prod.yml up -d
'''
```

### Notifications

Uncomment and configure the email notifications in the `post` section:

```groovy
emailext (
    subject: "✅ Build Success: ${APP_NAME} #${BUILD_NUMBER}",
    body: "Build completed successfully",
    to: "<EMAIL>"
)
```

## Troubleshooting

### Common Issues

1. **Docker permission denied**
   - Ensure Jenkins user is in docker group
   - Restart Jenkins service

2. **Credentials not found**
   - Verify credential IDs match exactly
   - Check credential scope (Global vs System)

3. **Pipeline fails on security scan**
   - Install Trivy on Jenkins agents
   - Configure Trivy cache directory

4. **Deployment timeouts**
   - Increase pipeline timeout
   - Check network connectivity

### Debugging

Enable debug logging by adding to pipeline:

```groovy
options {
    // Add debug logging
    skipDefaultCheckout(true)
}
```

Check Jenkins logs:
- Go to build > Console Output
- Look for specific error messages
- Check agent logs if using distributed builds

## Monitoring

### Build Metrics
- Monitor build success rate
- Track build duration trends
- Review security scan results

### Deployment Metrics
- Monitor deployment frequency
- Track deployment success rate
- Review rollback frequency

### Alerts
Set up alerts for:
- Build failures
- Security vulnerabilities
- Deployment failures
- Performance degradation

/**
 * Configuration file for Vietnam Administrative Restructuring 2025
 * This file manages environment variables and API configurations
 */

// Configuration object
const CONFIG = {
    // API Configuration
    api: {
        gemini: {
            // API key should be set via environment variable or loaded from .env file
            // For development, you can set this directly, but for production use environment variables
            apiKey: window.GEMINI_API_KEY || '',
            baseUrl: 'https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent',
            model: 'gemini-2.0-flash'
        }
    },
    
    // Application settings
    app: {
        name: 'Vietnam Administrative Restructuring 2025',
        version: '1.0.0',
        description: 'Interactive infographic showing the proposed administrative restructuring of Vietnam from 63 to 34 provincial-level units'
    },
    
    // Chart configuration
    charts: {
        defaultColors: {
            primary: '#4BB3FD',
            secondary: '#00487C',
            accent: '#027BCE',
            background: '#FFFFFF'
        },
        animation: {
            duration: 1000,
            easing: 'easeInOutQuart'
        }
    }
};

// Function to get API URL with key
CONFIG.getGeminiApiUrl = function() {
    if (!this.api.gemini.apiKey) {
        console.warn('Gemini API key is not configured. Please set GEMINI_API_KEY environment variable.');
        return null;
    }
    return `${this.api.gemini.baseUrl}?key=${this.api.gemini.apiKey}`;
};

// Export configuration for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = CONFIG;
} else {
    window.CONFIG = CONFIG;
}

/**
 * Environment loader for Vietnam Administrative Restructuring 2025
 * This script loads environment variables for client-side use
 * 
 * Note: For security reasons, API keys should never be exposed in client-side code
 * in production. This is for development/demo purposes only.
 * 
 * In production, consider:
 * 1. Using a backend proxy to handle API calls
 * 2. Using environment-specific build processes
 * 3. Using secure token exchange mechanisms
 */

(function() {
    'use strict';

    /**
     * Load environment variables from various sources
     */
    function loadEnvironmentVariables() {
        // Method 1: Check for environment variables set via build process or server
        if (typeof process !== 'undefined' && process.env) {
            return {
                GEMINI_API_KEY: process.env.GEMINI_API_KEY,
                APP_NAME: process.env.APP_NAME,
                APP_VERSION: process.env.APP_VERSION,
                APP_ENVIRONMENT: process.env.APP_ENVIRONMENT,
                GEMINI_API_BASE_URL: process.env.GEMINI_API_BASE_URL
            };
        }

        // Method 2: Check for variables set on window object (for development)
        if (typeof window !== 'undefined') {
            return {
                GEMINI_API_KEY: window.GEMINI_API_KEY,
                APP_NAME: window.APP_NAME,
                APP_VERSION: window.APP_VERSION,
                APP_ENVIRONMENT: window.APP_ENVIRONMENT,
                GEMINI_API_BASE_URL: window.GEMINI_API_BASE_URL
            };
        }

        // Method 3: Return empty object if no environment variables found
        return {};
    }

    /**
     * Set environment variables on window object for global access
     */
    function setGlobalEnvironmentVariables() {
        const env = loadEnvironmentVariables();
        
        // Set on window object for global access
        if (typeof window !== 'undefined') {
            window.ENV = env;
            
            // Set individual variables for backward compatibility
            Object.keys(env).forEach(key => {
                if (env[key] !== undefined) {
                    window[key] = env[key];
                }
            });
        }
    }

    /**
     * Load environment from a .env file (for development)
     * This requires a server to serve the .env file content
     */
    async function loadFromEnvFile() {
        try {
            const response = await fetch('.env');
            if (response.ok) {
                const envContent = await response.text();
                const envVars = parseEnvContent(envContent);
                
                // Set variables on window object
                if (typeof window !== 'undefined') {
                    Object.keys(envVars).forEach(key => {
                        window[key] = envVars[key];
                    });
                }
                
                return envVars;
            }
        } catch (error) {
            console.log('No .env file found or accessible. Using other environment sources.');
        }
        return {};
    }

    /**
     * Parse .env file content
     * @param {string} content - The .env file content
     * @returns {object} - Parsed environment variables
     */
    function parseEnvContent(content) {
        const envVars = {};
        const lines = content.split('\n');
        
        lines.forEach(line => {
            line = line.trim();
            if (line && !line.startsWith('#')) {
                const [key, ...valueParts] = line.split('=');
                if (key && valueParts.length > 0) {
                    let value = valueParts.join('=').trim();
                    // Remove quotes if present
                    if ((value.startsWith('"') && value.endsWith('"')) ||
                        (value.startsWith("'") && value.endsWith("'"))) {
                        value = value.slice(1, -1);
                    }
                    envVars[key.trim()] = value;
                }
            }
        });
        
        return envVars;
    }

    /**
     * Initialize environment loading
     */
    async function init() {
        // Try to load from .env file first (for development)
        await loadFromEnvFile();
        
        // Then load from other sources
        setGlobalEnvironmentVariables();
        
        console.log('Environment variables loaded');
    }

    // Auto-initialize when script loads
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', init);
    } else {
        init();
    }

    // Export for manual initialization if needed
    if (typeof window !== 'undefined') {
        window.loadEnvironment = init;
    }
})();

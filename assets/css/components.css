/* Component styles for Vietnam Administrative Restructuring 2025 */

/* Flow card components */
.flow-card {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

.flow-card-content {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-wrap: wrap;
}

.flow-item {
    background-color: #e0f2fe;
    color: #0c4a6e;
    padding: 0.5rem 1rem;
    border-radius: 9999px;
    margin: 0.5rem;
    font-weight: 500;
    border: 2px solid #7dd3fc;
}

.flow-arrow {
    font-size: 1.5rem;
    color: #0369a1;
    margin: 0 0.5rem;
}

.flow-result {
    background-color: #0496FF;
    color: white;
    padding: 0.75rem 1.5rem;
    border-radius: 9999px;
    margin: 0.5rem;
    font-weight: 700;
    border: 2px solid #00487C;
}

/* Button components */
.gemini-btn {
    background-color: #00487C;
    color: white;
    margin-top: 0.75rem;
    padding: 0.5rem 1rem;
    border-radius: 9999px;
    font-weight: 600;
    transition: background-color 0.3s;
}

.gemini-btn:hover {
    background-color: #027BCE;
}

/* AI Disclaimer Section */
.ai-disclaimer {
    background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
    border-left: 6px solid #f59e0b;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.ai-disclaimer-icon {
    animation: pulse-warning 2s infinite;
}

@keyframes pulse-warning {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
}

.ai-disclaimer-bullet {
    transition: transform 0.2s ease-in-out;
}

.ai-disclaimer-bullet:hover {
    transform: scale(1.2);
}

/* Responsive adjustments for disclaimer */
@media (max-width: 768px) {
    .ai-disclaimer {
        margin-left: -1rem;
        margin-right: -1rem;
        border-radius: 0;
    }

    .ai-disclaimer .container {
        padding-left: 1rem;
        padding-right: 1rem;
    }
}

/**
 * Modal manager for Vietnam Administrative Restructuring 2025
 * Handles modal display, loading states, and user interactions
 */

class ModalManager {
    constructor() {
        this.modal = null;
        this.modalContent = null;
        this.modalTitle = null;
        this.closeBtn = null;
        this.apiManager = new ApiManager();
        
        this.init();
    }

    /**
     * Initialize modal elements and event listeners
     */
    init() {
        this.modal = document.getElementById('geminiModal');
        this.modalContent = document.getElementById('modalContent');
        this.modalTitle = document.getElementById('modalTitle');
        this.closeBtn = document.getElementById('closeModalBtn');

        if (!this.modal || !this.modalContent || !this.modalTitle || !this.closeBtn) {
            console.error('Modal elements not found');
            return;
        }

        this.setupEventListeners();
        this.setupAnalyzeButtons();
    }

    /**
     * Set up event listeners for modal controls
     */
    setupEventListeners() {
        // Close button
        this.closeBtn.addEventListener('click', () => this.hide());

        // Click outside modal to close
        this.modal.addEventListener('click', (e) => {
            if (e.target === this.modal) {
                this.hide();
            }
        });

        // Escape key to close
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && !this.modal.classList.contains('hidden')) {
                this.hide();
            }
        });
    }

    /**
     * Set up analyze buttons throughout the page
     */
    setupAnalyzeButtons() {
        const analyzeButtons = document.querySelectorAll('.analyze-btn');
        
        analyzeButtons.forEach(button => {
            button.addEventListener('click', async () => {
                const provinces = button.dataset.provinces;
                if (provinces) {
                    await this.showAnalysis(provinces);
                }
            });
        });
    }

    /**
     * Show the modal
     */
    show() {
        if (this.modal) {
            this.modal.classList.remove('hidden');
            this.modal.classList.add('flex');
            document.body.style.overflow = 'hidden'; // Prevent background scrolling
        }
    }

    /**
     * Hide the modal
     */
    hide() {
        if (this.modal) {
            this.modal.classList.add('hidden');
            this.modal.classList.remove('flex');
            document.body.style.overflow = ''; // Restore scrolling
        }
    }

    /**
     * Show loading state
     */
    showLoading() {
        if (this.modalContent) {
            this.modalContent.innerHTML = `
                <div class="flex justify-center items-center h-48">
                    <div class="loader"></div>
                </div>
            `;
        }
    }

    /**
     * Show analysis for given provinces
     * @param {string} provinces - The provinces to analyze
     */
    async showAnalysis(provinces) {
        // Update modal title
        if (this.modalTitle) {
            this.modalTitle.innerText = `✨ Phân tích Tác động: ${provinces}`;
        }

        // Show modal and loading state
        this.show();
        this.showLoading();

        try {
            // Check if API is configured
            if (!this.apiManager.isConfigured()) {
                throw new Error('API chưa được cấu hình. Vui lòng thiết lập API key trong file cấu hình.');
            }

            // Generate analysis
            const analysisText = await this.apiManager.generateAnalysis(provinces);
            
            // Display the analysis
            this.showContent(analysisText);

        } catch (error) {
            console.error('Error generating analysis:', error);
            this.showError(error.message);
        }
    }

    /**
     * Show content in the modal
     * @param {string} content - The content to display
     */
    showContent(content) {
        if (this.modalContent) {
            // Format the content with proper line breaks
            const formattedContent = content.replace(/\n/g, '<br>');
            this.modalContent.innerHTML = `
                <div class="prose max-w-none text-gray-700">
                    ${formattedContent}
                </div>
            `;
        }
    }

    /**
     * Show error message in the modal
     * @param {string} errorMessage - The error message to display
     */
    showError(errorMessage) {
        if (this.modalContent) {
            this.modalContent.innerHTML = `
                <p class="text-red-500 text-center">
                    ${errorMessage}
                </p>
            `;
        }
    }

    /**
     * Get modal status
     * @returns {object} - Status information
     */
    getStatus() {
        return {
            initialized: !!(this.modal && this.modalContent && this.modalTitle && this.closeBtn),
            visible: this.modal && !this.modal.classList.contains('hidden'),
            apiStatus: this.apiManager.getStatus()
        };
    }
}

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ModalManager;
} else {
    window.ModalManager = ModalManager;
}

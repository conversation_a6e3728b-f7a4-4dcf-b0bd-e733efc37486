/**
 * Main application module for Vietnam Administrative Restructuring 2025
 * Coordinates all other modules and handles application initialization
 */

class App {
    constructor() {
        this.chartsManager = null;
        this.modalManager = null;
        this.initialized = false;
    }

    /**
     * Initialize the application
     */
    async init() {
        try {
            console.log('Initializing Vietnam Administrative Restructuring 2025 application...');

            // Wait for DOM to be ready
            if (document.readyState === 'loading') {
                await new Promise(resolve => {
                    document.addEventListener('DOMContentLoaded', resolve);
                });
            }

            // Initialize modules
            this.initializeCharts();
            this.initializeModal();
            
            // Set up global error handling
            this.setupErrorHandling();

            this.initialized = true;
            console.log('Application initialized successfully');

            // Log configuration status
            this.logStatus();

        } catch (error) {
            console.error('Failed to initialize application:', error);
            this.showInitializationError(error);
        }
    }

    /**
     * Initialize charts module
     */
    initializeCharts() {
        try {
            this.chartsManager = new ChartsManager();
            this.chartsManager.initializeCharts();
            console.log('Charts initialized successfully');
        } catch (error) {
            console.error('Failed to initialize charts:', error);
            throw new Error('Charts initialization failed');
        }
    }

    /**
     * Initialize modal module
     */
    initializeModal() {
        try {
            this.modalManager = new ModalManager();
            console.log('Modal manager initialized successfully');
        } catch (error) {
            console.error('Failed to initialize modal:', error);
            throw new Error('Modal initialization failed');
        }
    }

    /**
     * Set up global error handling
     */
    setupErrorHandling() {
        window.addEventListener('error', (event) => {
            console.error('Global error:', event.error);
        });

        window.addEventListener('unhandledrejection', (event) => {
            console.error('Unhandled promise rejection:', event.reason);
        });
    }

    /**
     * Show initialization error to user
     * @param {Error} error - The initialization error
     */
    showInitializationError(error) {
        const errorContainer = document.createElement('div');
        errorContainer.className = 'fixed top-4 right-4 bg-red-500 text-white p-4 rounded-lg shadow-lg z-50';
        errorContainer.innerHTML = `
            <h4 class="font-bold">Lỗi khởi tạo ứng dụng</h4>
            <p class="text-sm mt-1">${error.message}</p>
            <button onclick="this.parentElement.remove()" class="mt-2 text-xs underline">Đóng</button>
        `;
        document.body.appendChild(errorContainer);

        // Auto-remove after 10 seconds
        setTimeout(() => {
            if (errorContainer.parentElement) {
                errorContainer.remove();
            }
        }, 10000);
    }

    /**
     * Log application status
     */
    logStatus() {
        const status = {
            initialized: this.initialized,
            charts: this.chartsManager ? 'Ready' : 'Not initialized',
            modal: this.modalManager ? this.modalManager.getStatus() : 'Not initialized',
            config: window.CONFIG ? 'Loaded' : 'Not loaded'
        };

        console.log('Application Status:', status);

        // Show API configuration warning if needed
        if (this.modalManager && !this.modalManager.apiManager.isConfigured()) {
            console.warn('⚠️ Gemini API is not configured. AI analysis features will not work.');
            console.log('To enable AI features, please set the GEMINI_API_KEY environment variable or update the configuration.');
        }
    }

    /**
     * Get application status
     * @returns {object} - Application status
     */
    getStatus() {
        return {
            initialized: this.initialized,
            charts: this.chartsManager ? 'Ready' : 'Not initialized',
            modal: this.modalManager ? this.modalManager.getStatus() : 'Not initialized',
            config: window.CONFIG ? 'Loaded' : 'Not loaded'
        };
    }

    /**
     * Cleanup application resources
     */
    destroy() {
        if (this.chartsManager) {
            this.chartsManager.destroyCharts();
        }
        
        this.chartsManager = null;
        this.modalManager = null;
        this.initialized = false;
        
        console.log('Application destroyed');
    }
}

// Initialize application when script loads
const app = new App();

// Auto-initialize when DOM is ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => app.init());
} else {
    app.init();
}

// Export for use in other modules or debugging
if (typeof module !== 'undefined' && module.exports) {
    module.exports = App;
} else {
    window.App = App;
    window.app = app;
}

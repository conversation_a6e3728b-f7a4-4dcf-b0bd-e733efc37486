/**
 * API module for Vietnam Administrative Restructuring 2025
 * Handles all API calls, particularly to the Gemini AI service
 */

class ApiManager {
    constructor() {
        this.config = window.CONFIG || {};
    }

    /**
     * Generate analysis using Gemini AI
     * @param {string} provinces - The provinces to analyze
     * @returns {Promise<string>} - The analysis text
     */
    async generateAnalysis(provinces) {
        const apiUrl = this.config.getGeminiApiUrl();
        
        if (!apiUrl) {
            throw new Error('Gemini API is not properly configured. Please check your API key.');
        }

        const prompt = `Với vai trò là một chuyên gia kinh tế - xã hội, hãy phân tích ngắn gọn, có cấu trúc rõ ràng về các tác động tiềm năng (bao gồm cả thuận lợi và thách thức) khi sáp nhập các đơn vị hành chính sau tại Việt Nam: ${provinces}. Tr<PERSON>nh bày dưới dạng các gạch đầu dòng cho mỗi phần Thuận lợi và Thách thức.`;

        const chatHistory = [{ 
            role: "user", 
            parts: [{ text: prompt }] 
        }];

        const payload = { 
            contents: chatHistory 
        };

        try {
            const response = await fetch(apiUrl, {
                method: 'POST',
                headers: { 
                    'Content-Type': 'application/json' 
                },
                body: JSON.stringify(payload)
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const result = await response.json();
            
            // Extract the analysis text from the response
            if (result.candidates && 
                result.candidates.length > 0 &&
                result.candidates[0].content && 
                result.candidates[0].content.parts &&
                result.candidates[0].content.parts.length > 0) {
                
                return result.candidates[0].content.parts[0].text;
            } else {
                throw new Error('Invalid response format from Gemini API');
            }

        } catch (error) {
            console.error('Error calling Gemini API:', error);
            
            // Return a user-friendly error message
            if (error.message.includes('API key')) {
                throw new Error('API key không hợp lệ. Vui lòng kiểm tra cấu hình API key.');
            } else if (error.message.includes('HTTP error')) {
                throw new Error('Lỗi kết nối đến dịch vụ AI. Vui lòng thử lại sau.');
            } else {
                throw new Error('Đã xảy ra lỗi khi kết nối đến dịch vụ phân tích. Vui lòng kiểm tra lại kết nối mạng và thử lại.');
            }
        }
    }

    /**
     * Check if API is properly configured
     * @returns {boolean} - True if API is configured
     */
    isConfigured() {
        return !!(this.config.api && this.config.api.gemini && this.config.api.gemini.apiKey);
    }

    /**
     * Get API status information
     * @returns {object} - Status information
     */
    getStatus() {
        return {
            configured: this.isConfigured(),
            apiKey: this.config.api?.gemini?.apiKey ? 'Set' : 'Not set',
            baseUrl: this.config.api?.gemini?.baseUrl || 'Not configured'
        };
    }
}

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ApiManager;
} else {
    window.ApiManager = ApiManager;
}

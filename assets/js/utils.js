/**
 * Utility functions for Vietnam Administrative Restructuring 2025
 * Common helper functions used throughout the application
 */

const Utils = {
    /**
     * Format text for display (handle line breaks, etc.)
     * @param {string} text - The text to format
     * @returns {string} - Formatted text
     */
    formatText(text) {
        if (!text) return '';
        return text.replace(/\n/g, '<br>');
    },

    /**
     * Debounce function to limit function calls
     * @param {Function} func - The function to debounce
     * @param {number} wait - The wait time in milliseconds
     * @returns {Function} - Debounced function
     */
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    },

    /**
     * Check if an element is visible in the viewport
     * @param {Element} element - The element to check
     * @returns {boolean} - True if element is visible
     */
    isElementVisible(element) {
        if (!element) return false;
        
        const rect = element.getBoundingClientRect();
        return (
            rect.top >= 0 &&
            rect.left >= 0 &&
            rect.bottom <= (window.innerHeight || document.documentElement.clientHeight) &&
            rect.right <= (window.innerWidth || document.documentElement.clientWidth)
        );
    },

    /**
     * Smooth scroll to an element
     * @param {Element|string} target - The element or selector to scroll to
     * @param {number} offset - Optional offset from the top
     */
    scrollToElement(target, offset = 0) {
        const element = typeof target === 'string' ? document.querySelector(target) : target;
        if (!element) return;

        const elementPosition = element.getBoundingClientRect().top;
        const offsetPosition = elementPosition + window.pageYOffset - offset;

        window.scrollTo({
            top: offsetPosition,
            behavior: 'smooth'
        });
    },

    /**
     * Create a loading spinner element
     * @param {string} className - Optional additional CSS classes
     * @returns {HTMLElement} - The spinner element
     */
    createSpinner(className = '') {
        const spinner = document.createElement('div');
        spinner.className = `loader ${className}`;
        return spinner;
    },

    /**
     * Show a notification message
     * @param {string} message - The message to show
     * @param {string} type - The type of notification (success, error, warning, info)
     * @param {number} duration - How long to show the notification (ms)
     */
    showNotification(message, type = 'info', duration = 5000) {
        const notification = document.createElement('div');
        const typeClasses = {
            success: 'bg-green-500',
            error: 'bg-red-500',
            warning: 'bg-yellow-500',
            info: 'bg-blue-500'
        };

        notification.className = `fixed top-4 right-4 ${typeClasses[type] || typeClasses.info} text-white p-4 rounded-lg shadow-lg z-50 max-w-sm`;
        notification.innerHTML = `
            <div class="flex items-center justify-between">
                <span>${message}</span>
                <button onclick="this.parentElement.parentElement.remove()" class="ml-4 text-white hover:text-gray-200">
                    ×
                </button>
            </div>
        `;

        document.body.appendChild(notification);

        // Auto-remove after duration
        if (duration > 0) {
            setTimeout(() => {
                if (notification.parentElement) {
                    notification.remove();
                }
            }, duration);
        }

        return notification;
    },

    /**
     * Validate API key format
     * @param {string} apiKey - The API key to validate
     * @returns {boolean} - True if API key appears valid
     */
    validateApiKey(apiKey) {
        if (!apiKey || typeof apiKey !== 'string') return false;
        
        // Basic validation - API keys should be non-empty strings
        // You can add more specific validation based on the API provider's format
        return apiKey.trim().length > 0 && apiKey !== 'your_api_key_here';
    },

    /**
     * Get browser information
     * @returns {object} - Browser information
     */
    getBrowserInfo() {
        const ua = navigator.userAgent;
        return {
            userAgent: ua,
            isMobile: /Mobile|Android|iPhone|iPad/.test(ua),
            isChrome: /Chrome/.test(ua),
            isFirefox: /Firefox/.test(ua),
            isSafari: /Safari/.test(ua) && !/Chrome/.test(ua),
            isEdge: /Edge/.test(ua)
        };
    },

    /**
     * Copy text to clipboard
     * @param {string} text - The text to copy
     * @returns {Promise<boolean>} - True if successful
     */
    async copyToClipboard(text) {
        try {
            if (navigator.clipboard && window.isSecureContext) {
                await navigator.clipboard.writeText(text);
                return true;
            } else {
                // Fallback for older browsers
                const textArea = document.createElement('textarea');
                textArea.value = text;
                textArea.style.position = 'fixed';
                textArea.style.left = '-999999px';
                textArea.style.top = '-999999px';
                document.body.appendChild(textArea);
                textArea.focus();
                textArea.select();
                const result = document.execCommand('copy');
                textArea.remove();
                return result;
            }
        } catch (error) {
            console.error('Failed to copy text:', error);
            return false;
        }
    },

    /**
     * Format numbers with Vietnamese locale
     * @param {number} number - The number to format
     * @returns {string} - Formatted number
     */
    formatNumber(number) {
        return new Intl.NumberFormat('vi-VN').format(number);
    },

    /**
     * Generate a simple UUID
     * @returns {string} - A UUID string
     */
    generateUUID() {
        return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
            const r = Math.random() * 16 | 0;
            const v = c === 'x' ? r : (r & 0x3 | 0x8);
            return v.toString(16);
        });
    }
};

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = Utils;
} else {
    window.Utils = Utils;
}

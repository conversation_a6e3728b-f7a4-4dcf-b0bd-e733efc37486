/**
 * Charts module for Vietnam Administrative Restructuring 2025
 * Handles all chart creation and configuration using Chart.js
 */

class ChartsManager {
    constructor() {
        this.charts = {};
        this.defaultTooltipConfig = {
            plugins: {
                tooltip: {
                    callbacks: {
                        title: function(tooltipItems) {
                            const item = tooltipItems[0];
                            let label = item.chart.data.labels[item.dataIndex];
                            if (Array.isArray(label)) {
                                return label.join(' ');
                            } else {
                                return label;
                            }
                        }
                    }
                },
                legend: {
                    labels: {
                        color: '#374151'
                    }
                }
            }
        };
    }

    /**
     * Initialize all charts
     */
    initializeCharts() {
        this.createProportionChart();
        this.createRegionalImpactChart();
    }

    /**
     * Create the proportion doughnut chart
     */
    createProportionChart() {
        const ctx = document.getElementById('proportionChart');
        if (!ctx) {
            console.error('Proportion chart canvas not found');
            return;
        }

        this.charts.proportion = new Chart(ctx.getContext('2d'), {
            type: 'doughnut',
            data: {
                labels: ['Tỉnh thành sáp nhập', 'Tỉnh thành giữ nguyên'],
                datasets: [{
                    label: 'Tỷ lệ',
                    data: [50, 13],
                    backgroundColor: [CONFIG.charts.defaultColors.primary, CONFIG.charts.defaultColors.secondary],
                    borderColor: [CONFIG.charts.defaultColors.background, CONFIG.charts.defaultColors.background],
                    borderWidth: 2
                }]
            },
            options: {
                ...this.defaultTooltipConfig,
                responsive: true,
                maintainAspectRatio: false
            }
        });
    }

    /**
     * Create the regional impact bar chart
     */
    createRegionalImpactChart() {
        const ctx = document.getElementById('regionalImpactChart');
        if (!ctx) {
            console.error('Regional impact chart canvas not found');
            return;
        }

        this.charts.regionalImpact = new Chart(ctx.getContext('2d'), {
            type: 'bar',
            data: {
                labels: ['Miền Bắc', 'Miền Trung & Tây Nguyên', 'Miền Nam'],
                datasets: [
                    {
                        label: 'Trước sáp nhập',
                        data: [25, 19, 19],
                        backgroundColor: CONFIG.charts.defaultColors.primary,
                        borderColor: CONFIG.charts.defaultColors.accent,
                        borderWidth: 1
                    },
                    {
                        label: 'Sau sáp nhập (dự kiến)',
                        data: [14, 11, 9],
                        backgroundColor: CONFIG.charts.defaultColors.secondary,
                        borderColor: '#003359',
                        borderWidth: 1
                    }
                ]
            },
            options: {
                ...this.defaultTooltipConfig,
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: { color: '#374151' }
                    },
                    x: {
                        ticks: { color: '#374151' }
                    }
                }
            }
        });
    }

    /**
     * Destroy all charts (useful for cleanup)
     */
    destroyCharts() {
        Object.values(this.charts).forEach(chart => {
            if (chart && typeof chart.destroy === 'function') {
                chart.destroy();
            }
        });
        this.charts = {};
    }
}

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ChartsManager;
} else {
    window.ChartsManager = ChartsManager;
}

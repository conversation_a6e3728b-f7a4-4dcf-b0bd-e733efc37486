# GitLab CI/CD Configuration Fixes

## Issues Resolved

### 1. ❌ **Global before_script Error**
**Problem**: GitLab CI/CD does not support global `before_script` at the root level.

**Error Message**: 
```
"This GitLab CI configuration is invalid: before_script config should be a string or a nested array of strings up to 10 levels deep"
```

**Solution**: Removed the global `before_script` and added individual `before_script` sections to each job that needs pipeline information logging.

**Before**:
```yaml
# Global before_script for Docker setup
before_script:
  - echo "🚀 Starting pipeline for ${APP_NAME} version ${APP_VERSION}"
  - echo "📋 Pipeline ID: ${CI_PIPELINE_ID}"
  - echo "🌿 Branch: ${CI_COMMIT_REF_NAME}"
```

**After**:
```yaml
# Individual before_script in each job
validate:structure:
  before_script:
    - 'echo "🚀 Starting pipeline for ${APP_NAME} version ${APP_VERSION}"'
    - 'echo "📋 Pipeline ID: ${CI_PIPELINE_ID}"'
    - 'echo "🌿 Branch: ${CI_COMMIT_REF_NAME}"'
```

### 2. ❌ **YAML String Parsing Issues**
**Problem**: Echo statements with colons were being parsed as YAML key-value pairs instead of strings.

**Solution**: Added single quotes around all echo statements containing colons to ensure proper string parsing.

**Before**:
```yaml
before_script:
  - echo "📋 Pipeline ID: ${CI_PIPELINE_ID}"  # Parsed as {echo "📋 Pipeline ID": "${CI_PIPELINE_ID}"}
```

**After**:
```yaml
before_script:
  - 'echo "📋 Pipeline ID: ${CI_PIPELINE_ID}"'  # Parsed as string
```

### 3. ❌ **Artifacts Configuration**
**Problem**: Incorrect artifact report types that are not supported in all GitLab versions.

**Solution**: Changed from specialized report types to generic `paths` artifacts.

**Before**:
```yaml
artifacts:
  reports:
    dependency_scanning: dependencies.txt
    container_scanning: trivy-report.json
```

**After**:
```yaml
artifacts:
  paths:
    - dependencies.txt
    - trivy-report.json
  when: always
```

## Validation Results

### ✅ **All Issues Resolved**

```
🔍 Validating GitLab CI/CD Configuration...
✅ YAML syntax is valid
✅ Required sections present
✅ Stages defined: ['validate', 'security', 'build', 'test', 'security-scan', 'deploy-staging', 'deploy-production']
✅ Jobs defined: 9
✅ All jobs have valid structure
✅ Variables section validated
✅ Docker jobs found: 6
✅ Environment jobs found: 2

🎉 GitLab CI/CD configuration validation completed successfully!

📋 Summary:
   - Stages: 7
   - Jobs: 9
   - Docker jobs: 6
   - Environment jobs: 2
```

## Pipeline Structure

### **Stages**
1. **validate** - Project structure validation
2. **security** - Secret detection and dependency scanning
3. **build** - Docker image building
4. **test** - Functional and performance testing
5. **security-scan** - Container vulnerability scanning
6. **deploy-staging** - Staging environment deployment
7. **deploy-production** - Production environment deployment

### **Jobs**
1. `validate:structure` - Validates project file structure
2. `security:secrets` - Scans for hardcoded secrets
3. `security:dependencies` - Checks external dependencies
4. `build:docker` - Builds and pushes Docker image
5. `test:functional` - Tests application functionality
6. `test:performance` - Tests application performance
7. `security:container-scan` - Scans Docker image for vulnerabilities
8. `deploy:staging` - Deploys to staging environment
9. `deploy:production` - Deploys to production environment

## Environment Variables Required

### **GitLab CI/CD Variables to Configure**

| Variable | Type | Environment | Description |
|----------|------|-------------|-------------|
| `STAGING_GEMINI_API_KEY` | Masked | staging | Gemini API key for staging |
| `PRODUCTION_GEMINI_API_KEY` | Masked, Protected | production | Gemini API key for production |

### **Built-in Variables Used**
- `CI_PIPELINE_ID` - Unique pipeline identifier
- `CI_COMMIT_BRANCH` - Current branch name
- `CI_DEFAULT_BRANCH` - Default branch (main/master)
- `CI_REGISTRY_*` - GitLab Container Registry credentials
- `APP_NAME` - Application name (defined in variables)
- `APP_VERSION` - Application version (pipeline ID)

## Testing the Configuration

### **Local Validation**
```bash
# Validate YAML syntax
python3 -c "import yaml; yaml.safe_load(open('.gitlab-ci.yml')); print('✅ Valid')"

# Run comprehensive validation
python3 validate-gitlab-ci.py
```

### **GitLab CI/CD Lint**
1. Go to your GitLab project
2. Navigate to **CI/CD > Pipelines**
3. Click **CI Lint** tab
4. Paste the `.gitlab-ci.yml` content
5. Click **Validate**

## Deployment Workflow

### **Staging Deployment**
- **Trigger**: Automatic on `develop` branch push
- **Manual**: Available for `main` branch
- **Environment**: staging
- **URL**: http://staging.vietnam-admin-restructuring.local

### **Production Deployment**
- **Trigger**: Manual approval required
- **Branch**: `main` branch only
- **Environment**: production
- **URL**: http://vietnam-admin-restructuring.gov.vn

## Next Steps

1. **Configure GitLab CI/CD Variables**
   - Add `STAGING_GEMINI_API_KEY` (masked, staging scope)
   - Add `PRODUCTION_GEMINI_API_KEY` (masked, protected, production scope)

2. **Set Up GitLab Runner**
   - Ensure Docker executor is enabled
   - Enable privileged mode for Docker-in-Docker

3. **Test the Pipeline**
   - Create a feature branch
   - Push changes to trigger pipeline
   - Verify all stages complete successfully

4. **Configure Branch Protection**
   - Protect `main` branch
   - Require merge request approval
   - Enable pipeline success requirement

## Troubleshooting

### **Common Issues**

1. **Docker-in-Docker Issues**
   - Ensure GitLab Runner has privileged mode enabled
   - Check Docker service availability

2. **Registry Authentication**
   - Verify `CI_REGISTRY_USER` and `CI_REGISTRY_PASSWORD` are available
   - Check GitLab Container Registry permissions

3. **Environment Variable Issues**
   - Verify variables are set in GitLab project settings
   - Check variable scope (environment-specific)

4. **Pipeline Validation**
   - Use GitLab CI Lint to validate configuration
   - Check job dependencies and stage order

---

**Status**: ✅ **All GitLab CI/CD configuration issues resolved and validated**

The pipeline is now ready for use with the Vietnam Administrative Restructuring 2025 project!

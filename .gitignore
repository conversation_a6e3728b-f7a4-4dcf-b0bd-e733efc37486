# Vietnam Administrative Restructuring 2025 - Git Ignore File
# This file specifies intentionally untracked files that Git should ignore

# ===========================
# Environment & Configuration
# ===========================

# Environment variables (contains sensitive API keys)
.env
.env.local
.env.development
.env.test
.env.production
.env.staging

# Local configuration files
config.local.js
config.local.json

# ===========================
# Dependencies & Packages
# ===========================

# Node.js dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
package-lock.json
yarn.lock

# Bower dependencies
bower_components/

# ===========================
# Build & Distribution
# ===========================

# Build outputs
dist/
build/
out/
.next/
.nuxt/

# Compiled assets
*.min.js
*.min.css
*.bundle.js
*.bundle.css

# Source maps
*.map

# ===========================
# IDE & Editor Files
# ===========================

# Visual Studio Code
.vscode/
*.code-workspace

# IntelliJ IDEA / WebStorm
.idea/
*.iml
*.ipr
*.iws

# Sublime Text
*.sublime-project
*.sublime-workspace

# Vim
*.swp
*.swo
*~

# Emacs
*~
\#*\#
/.emacs.desktop
/.emacs.desktop.lock
*.elc
auto-save-list
tramp
.\#*

# Atom
.atom/

# ===========================
# Operating System Files
# ===========================

# macOS
.DS_Store
.AppleDouble
.LSOverride
Icon?
._*
.DocumentRevisions-V100
.fseventsd
.Spotlight-V100
.TemporaryItems
.Trashes
.VolumeIcon.icns
.com.apple.timemachine.donotpresent
.AppleDB
.AppleDesktop
Network Trash Folder
Temporary Items
.apdisk

# Windows
Thumbs.db
Thumbs.db:encryptable
ehthumbs.db
ehthumbs_vista.db
*.tmp
*.temp
Desktop.ini
$RECYCLE.BIN/
*.cab
*.msi
*.msix
*.msm
*.msp
*.lnk

# Linux
*~
.fuse_hidden*
.directory
.Trash-*
.nfs*

# ===========================
# Logs & Runtime Files
# ===========================

# Log files
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*

# Runtime data
pids/
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov
.nyc_output

# ===========================
# Docker & Containerization
# ===========================

# Docker
docker-compose.override.yml
docker-compose.local.yml

# ===========================
# CI/CD & Deployment
# ===========================

# Jenkins
.jenkins/

# GitHub Actions
.github/workflows/*.local.yml

# Deployment artifacts
deploy/
deployment/

# ===========================
# Testing & Quality Assurance
# ===========================

# Test results
test-results/
coverage/
.nyc_output/

# Jest
jest.config.local.js

# Cypress
cypress/videos/
cypress/screenshots/

# ===========================
# Temporary & Cache Files
# ===========================

# Temporary files
tmp/
temp/
.tmp/
.temp/

# Cache directories
.cache/
.parcel-cache/
.eslintcache
.stylelintcache

# ===========================
# Security & Sensitive Data
# ===========================

# SSL certificates
*.pem
*.key
*.crt
*.csr

# API keys and secrets (additional patterns)
secrets/
.secrets/
api-keys.txt
credentials.json

# ===========================
# Project Specific
# ===========================

# Test files (keep test.html for development but ignore in production)
test.html

# Backup files
*.backup
*.bak
*.orig

# Archive files
*.zip
*.tar.gz
*.rar
*.7z

# ===========================
# Optional: Uncomment if needed
# ===========================

# Uncomment the following lines if you're using these tools:

# ESLint
# .eslintrc.local.js

# Prettier
# .prettierrc.local

# TypeScript
# *.tsbuildinfo

# Webpack
# webpack.config.local.js

# Babel
# babel.config.local.js
